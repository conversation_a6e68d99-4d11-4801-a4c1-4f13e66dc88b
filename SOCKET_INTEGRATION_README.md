# Socket.IO Frontend Integration

This document explains how to use the Socket.IO real-time features in your UploadDoc frontend application.

## Overview

The Socket.IO integration provides real-time functionality for:
- **Chat messaging** - Real-time messaging between students and admins
- **Notifications** - Instant notifications for various events  
- **Project status updates** - Live updates when project status changes
- **User online/offline status** - Track who's currently online

## Architecture

### Core Components

1. **socketService** (`sockets/socketService.js`) - Main Socket.IO client service
2. **chatHandlers** (`sockets/chatHandlers.js`) - Chat-specific functionality
3. **notificationHandlers** (`sockets/notificationHandlers.js`) - Notification handling
4. **projectHandlers** (`sockets/projectHandlers.js`) - Project update handling
5. **SocketContext** (`app/context/SocketContext.js`) - React context for Socket.IO
6. **useSocket** hook - Access socket functionality in components
7. **useChat** hook (`hooks/useChat.js`) - Simplified chat integration

## Setup

### 1. Install Dependencies

The Socket.IO client is already installed:
```bash
npm install socket.io-client
```

### 2. Environment Variables

Ensure your `.env.local` has:
```env
NEXT_PUBLIC_BACKEND_URL=http://localhost:5000
```

### 3. Provider Setup

The `SocketProvider` is already integrated in your `app/layout.js`. It automatically:
- Connects when a user logs in
- Disconnects when a user logs out
- Handles authentication with JWT tokens
- Sets up all event handlers

## Usage Examples

### Basic Socket Connection Status

```jsx
import { useSocket } from '../app/context/SocketContext';

function MyComponent() {
  const { isConnected, connectionStatus } = useSocket();
  
  return (
    <div>
      Status: {isConnected ? 'Connected' : 'Disconnected'}
      Details: {connectionStatus}
    </div>
  );
}
```

### Chat Integration

```jsx
import { useChat } from '../hooks/useChat';

function ChatComponent({ chatId, projectId }) {
  const {
    messages,
    sendMessage,
    isJoined,
    typingUsers,
    markMessagesRead
  } = useChat(chatId, projectId);

  const handleSend = (message) => {
    sendMessage(message);
  };

  return (
    <div>
      {messages.map(msg => (
        <div key={msg._id}>{msg.message}</div>
      ))}
      {typingUsers.length > 0 && <div>Someone is typing...</div>}
    </div>
  );
}
```

### Project Updates

```jsx
import { useSocket } from '../app/context/SocketContext';
import { useEffect } from 'react';

function ProjectDashboard() {
  const { projectHandlers, subscribeToProjectUpdates } = useSocket();

  useEffect(() => {
    // Subscribe to project updates
    subscribeToProjectUpdates('project-id-123');

    // Listen for project status changes
    const handleStatusUpdate = (projectData) => {
      console.log('Project status updated:', projectData);
      // Update your UI accordingly
    };

    projectHandlers.onProjectStatusUpdate(handleStatusUpdate);

    // Cleanup
    return () => {
      projectHandlers.removeProjectStatusUpdateCallback(handleStatusUpdate);
    };
  }, []);

  return <div>Project Dashboard</div>;
}
```

### Notifications

```jsx
import { useSocket } from '../app/context/SocketContext';
import { useEffect } from 'react';

function NotificationComponent() {
  const { notificationHandlers } = useSocket();

  useEffect(() => {
    const handleNewNotification = (notification) => {
      // Handle new notification
      console.log('New notification:', notification);
    };

    notificationHandlers.onNewNotification(handleNewNotification);

    // Subscribe to notification types
    notificationHandlers.subscribeToNotifications([
      'chat_message',
      'project_status',
      'project_progress'
    ]);

    return () => {
      notificationHandlers.removeNewNotificationCallback(handleNewNotification);
    };
  }, []);

  return <div>Notifications will appear here</div>;
}
```

## Socket Events

### Chat Events
- `join_chat` - Join a chat room
- `leave_chat` - Leave a chat room
- `send_message` - Send a message
- `new_message` - Receive a new message
- `typing_start/stop` - Typing indicators
- `mark_messages_read` - Mark messages as read

### Notification Events
- `new_notification` - New notification received
- `mark_notification_read` - Mark notification as read
- `subscribe_to_notifications` - Subscribe to notification types

### Project Events
- `project_status_updated` - Project status changed
- `project_progress_updated` - Project progress updated
- `project_reassigned` - Project reassigned to new user
- `project_deadline_updated` - Project deadline changed

### User Status Events
- `user_online` - User came online
- `user_offline` - User went offline

## Available Hooks and Functions

### useSocket Hook

```jsx
const {
  // Connection state
  isConnected,
  connectionStatus,
  
  // User status
  onlineUsers,
  isUserOnline,
  
  // Chat functions
  sendMessage,
  joinChat,
  leaveChat,
  markMessagesRead,
  
  // Project functions
  subscribeToProjectUpdates,
  updateProjectStatus,
  
  // Direct handler access
  chatHandlers,
  notificationHandlers,
  projectHandlers
} = useSocket();
```

### useChat Hook

```jsx
const {
  // State
  messages,
  typingUsers,
  participants,
  isJoined,
  loading,
  
  // Actions
  sendMessage,
  sendFile,
  markMessagesRead,
  startTyping,
  stopTyping,
  
  // Utilities
  getUnreadCount,
  getLastMessage
} = useChat(chatId, projectId);
```

## Browser Notifications

The integration automatically requests browser notification permissions and shows notifications for:
- New chat messages
- Project status updates
- Important system notifications

Notifications will automatically navigate to the relevant page when clicked.

## Error Handling

The Socket.IO integration includes automatic:
- Reconnection on connection loss
- Authentication error handling
- Connection timeout handling
- Toast notifications for important events

## Socket Connection Status Indicator

A `SocketStatus` component is automatically included in your layout to show the connection status with a colored indicator.

## Example Implementation

Check `components/examples/SocketChatExample.jsx` for a complete chat implementation example.

## Best Practices

1. **Always check connection status** before emitting events
2. **Clean up event listeners** in useEffect cleanup functions
3. **Handle offline scenarios** gracefully
4. **Use the provided hooks** instead of direct socket access
5. **Subscribe to relevant events only** to avoid unnecessary updates

## Troubleshooting

### Connection Issues
- Check `NEXT_PUBLIC_BACKEND_URL` environment variable
- Ensure backend Socket.IO server is running
- Check browser network tab for WebSocket connection errors

### Authentication Issues
- Verify JWT token is stored in localStorage
- Check token expiration
- Ensure backend authentication middleware is working

### Event Not Firing
- Check if event names match between frontend and backend
- Verify event listeners are properly registered
- Check browser console for Socket.IO debug messages

## Advanced Usage

For advanced use cases, you can access the raw socket service:

```jsx
import { socketService } from '../sockets';

// Wait for connection
await socketService.waitForConnection();

// Emit custom events
socketService.emit('custom_event', { data: 'example' });

// Listen to custom events
socketService.on('custom_response', (data) => {
  console.log('Custom response:', data);
});
```

## Next Steps

1. Test the integration with your backend
2. Customize notification handling for your use case
3. Implement real-time features in your existing components
4. Add error boundaries for Socket.IO components
5. Monitor Socket.IO performance and connection stability

For more details, check the individual handler files and the example components.
