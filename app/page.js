import Navbar from '@/components/Navbar';
import { StructuredData } from '@/components/SEO';
import {
	generateStructuredData,
	generateServiceStructuredData,
	generateFAQStructuredData,
} from '@/lib/seo';
import HeroSection from '@/components/Home/HeroSection';
import ProductShowcase from '@/components/Home/ProductShowcase';
import FeaturesSection from '@/components/Home/FeaturesSection';
import TestimonialsSection from '@/components/Home/TestimonialsSection';
import CTASection from '@/components/Home/CTASection';

const Home = () => {
	// Structured data for the homepage
	const websiteStructuredData = generateStructuredData({
		type: 'WebSite',
		name: 'UploadDoc',
		description:
			'Document management platform for students, professionals, and vendors.',
		url: 'https://uploaddoc.app',
		additionalData: {
			potentialAction: {
				'@type': 'SearchAction',
				target:
					'https://uploaddoc.app/findprovider?search={search_term_string}',
				'query-input': 'required name=search_term_string',
			},
		},
	});

	const serviceStructuredData = generateServiceStructuredData({
		name: 'Document Management Platform',
		description:
			'Secure document upload, management, and printing services connecting users with trusted vendors.',
		serviceType: 'Document Management',
		areaServed: 'Worldwide',
		additionalData: {
			hasOfferCatalog: {
				'@type': 'OfferCatalog',
				name: 'UploadDoc Services',
				itemListElement: [
					{
						'@type': 'Offer',
						itemOffered: {
							'@type': 'Service',
							name: 'Document Upload',
							description: 'Secure document upload and storage',
						},
					},
					{
						'@type': 'Offer',
						itemOffered: {
							'@type': 'Service',
							name: 'Printing Services',
							description: 'Connect with trusted printing vendors',
						},
					},
					{
						'@type': 'Offer',
						itemOffered: {
							'@type': 'Service',
							name: 'Document Management',
							description: 'Organize and track your documents',
						},
					},
				],
			},
		},
	});

	const faqStructuredData = generateFAQStructuredData([
		{
			question: 'How do I submit documents on UploadDoc?',
			answer:
				'Simply create an account, navigate to the Submit page, select your documents, choose a vendor, and upload. Your documents will be securely stored and sent to the selected vendor.',
		},
		{
			question: 'Is my document data secure?',
			answer:
				'Yes, UploadDoc uses enterprise-grade security measures to protect your documents. All files are encrypted and only accessible by authorized vendors you choose.',
		},
		{
			question: 'How do I find printing providers near me?',
			answer:
				'Use our Find Provider feature to search for printing vendors in your area. You can filter by location, ratings, services, and pricing to find the perfect match.',
		},
		{
			question: 'Can I become a vendor on UploadDoc?',
			answer:
				'Yes! Visit our Become a Vendor page to apply. We welcome printing businesses of all sizes to join our platform and connect with customers.',
		},
	]);

	return (
		<div className='min-h-screen bg-background'>
			{/* Structured Data */}
			<StructuredData data={websiteStructuredData} />
			<StructuredData data={serviceStructuredData} />
			<StructuredData data={faqStructuredData} />

			<Navbar />
			<HeroSection />
			<ProductShowcase />
			<FeaturesSection />
			<TestimonialsSection />
			<CTASection />
		</div>
	);
};

export default Home;
