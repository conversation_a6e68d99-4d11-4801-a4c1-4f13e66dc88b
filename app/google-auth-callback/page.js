'use client';
import { useRouter } from 'next/navigation';
import { useEffect, useContext } from 'react';
import { AuthContext } from '@/app/context/AuthContext';

const GoogleAuthCallback = () => {
	const router = useRouter();
	const { handleGoogleCallback } = useContext(AuthContext);

	useEffect(() => {
		const processCallback = async () => {
			try {
				// Try the old callback method for backward compatibility
				await handleGoogleCallback();
				router.push('/submit');
			} catch (error) {
				console.error('Google login error:', error);
				// Redirect to login page with a message
				router.push('/auth/login?error=google_callback_deprecated');
			}
		};

		// Show deprecation warning
		console.warn(
			'Google Auth Callback page is deprecated. The app now uses client-side Google Sign-In.',
		);

		// Check if there's a token in the URL (old method)
		const urlParams = new URLSearchParams(window.location.search);
		const token = urlParams.get('token');

		if (token) {
			// Process the old callback
			processCallback();
		} else {
			// No token, redirect to login
			setTimeout(() => {
				router.push('/auth/login');
			}, 2000);
		}
	}, [handleGoogleCallback, router]);

	return (
		<div className='min-h-screen flex items-center justify-center bg-background text-text'>
			<div className='text-center'>
				<div className='animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4'></div>
				<p>Processing login...</p>
				<p className='text-sm text-text/60 mt-2'>
					If this takes too long, you'll be redirected to the login page.
				</p>
			</div>
		</div>
	);
};

export default GoogleAuthCallback;
