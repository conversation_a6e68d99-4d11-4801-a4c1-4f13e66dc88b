'use client';
import React, { createContext, useContext, useEffect, useState, useCallback, useRef } from 'react';
import { useRouter } from 'next/navigation';
import socketService from '../../sockets/socketService';
import chatHandlers from '../../sockets/chatHandlers';
import notificationHandlers from '../../sockets/notificationHandlers';
import projectHandlers from '../../sockets/projectHandlers';
import { AuthContext } from './AuthContext';
import toast from 'react-hot-toast';

const SocketContext = createContext();

export const SocketProvider = ({ children }) => {
  const { user } = useContext(AuthContext);
  const router = useRouter();
  
  const [isConnected, setIsConnected] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState('disconnected');
  const [onlineUsers, setOnlineUsers] = useState(new Map());
  const reconnectTimeoutRef = useRef(null);

  // Initialize socket connection
  const initializeSocket = useCallback(async () => {
    if (!user) {
      console.log('No user found, cannot initialize socket');
      return;
    }

    const token = localStorage.getItem('token');
    if (!token) {
      console.log('No token found, cannot initialize socket');
      return;
    }

    try {
      setConnectionStatus('connecting');
      console.log('Initializing socket connection...');
      
      // Connect to socket
      socketService.connect(token);
      
      // Initialize all handlers
      chatHandlers.initialize();
      notificationHandlers.initialize();
      projectHandlers.initialize();
      
      // Set up socket event listeners
      setupSocketEventListeners();
      
    } catch (error) {
      console.error('Failed to initialize socket:', error);
      setConnectionStatus('error');
      toast.error('Failed to connect to real-time services');
    }
  }, [user]);

  // Setup socket event listeners
  const setupSocketEventListeners = useCallback(() => {
    // Connection events
    socketService.on('connection_established', (data) => {
      console.log('Socket connection established:', data);
      setIsConnected(true);
      setConnectionStatus('connected');
      toast.success('Connected to real-time services');
      
      // Clear any reconnect timeout
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current);
        reconnectTimeoutRef.current = null;
      }
    });

    socketService.on('disconnected', (data) => {
      console.log('Socket disconnected:', data);
      setIsConnected(false);
      setConnectionStatus('disconnected');
      
      // Attempt to reconnect after 3 seconds
      reconnectTimeoutRef.current = setTimeout(() => {
        if (user && localStorage.getItem('token')) {
          console.log('Attempting to reconnect...');
          initializeSocket();
        }
      }, 3000);
    });

    socketService.on('reconnected', (data) => {
      console.log('Socket reconnected:', data);
      setIsConnected(true);
      setConnectionStatus('connected');
      toast.success('Reconnected to real-time services');
    });

    socketService.on('connection_failed', (error) => {
      console.error('Socket connection failed:', error);
      setIsConnected(false);
      setConnectionStatus('error');
    });

    socketService.on('authentication_error', (error) => {
      console.error('Socket authentication error:', error);
      setIsConnected(false);
      setConnectionStatus('auth_error');
      toast.error('Authentication failed for real-time services');
    });

    // User status events
    socketService.on('user_online', (userData) => {
      console.log('User came online:', userData);
      setOnlineUsers(prev => new Map(prev.set(userData.userId, userData)));
    });

    socketService.on('user_offline', (userData) => {
      console.log('User went offline:', userData);
      setOnlineUsers(prev => {
        const newMap = new Map(prev);
        newMap.delete(userData.userId);
        return newMap;
      });
    });

    // Set up notification handlers
    notificationHandlers.onNewNotification((notification) => {
      // Show toast notification
      toast.success(notification.message || notification.content, {
        duration: 5000,
        icon: '🔔'
      });
    });

    // Set up project update handlers
    projectHandlers.onProjectStatusUpdate((projectData) => {
      toast.success(`Project ${projectData.title || projectData.name || 'Unknown'} status updated to: ${projectData.status}`, {
        duration: 4000,
        icon: '📋'
      });
    });

    projectHandlers.onProjectReassigned((projectData) => {
      toast.info(`Project ${projectData.title || projectData.name || 'Unknown'} has been reassigned`, {
        duration: 4000,
        icon: '👤'
      });
    });

    projectHandlers.onProjectDeadlineUpdate((projectData) => {
      toast.warning(`Project ${projectData.title || projectData.name || 'Unknown'} deadline has been updated`, {
        duration: 4000,
        icon: '⏰'
      });
    });

  }, [user, initializeSocket]);

  // Disconnect socket
  const disconnectSocket = useCallback(() => {
    console.log('Disconnecting socket...');
    
    // Clear reconnect timeout
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }
    
    // Cleanup handlers
    chatHandlers.cleanup();
    notificationHandlers.cleanup();
    projectHandlers.cleanup();
    
    // Disconnect socket
    socketService.disconnect();
    
    // Reset state
    setIsConnected(false);
    setConnectionStatus('disconnected');
    setOnlineUsers(new Map());
  }, []);

  // Effect to handle user login/logout
  useEffect(() => {
    if (user && localStorage.getItem('token')) {
      initializeSocket();
    } else {
      disconnectSocket();
    }

    // Cleanup on unmount
    return () => {
      disconnectSocket();
    };
  }, [user, initializeSocket, disconnectSocket]);

  // Auto-subscribe to user's projects when connected
  useEffect(() => {
    if (isConnected && user) {
      // Subscribe to notifications
      notificationHandlers.subscribeToNotifications([
        'chat_message',
        'project_status',
        'project_progress',
        'project_reassigned',
        'deadline_update'
      ]);

      // If user has projects, subscribe to project updates
      if (user.projects && Array.isArray(user.projects)) {
        projectHandlers.autoSubscribeToUserProjects(user.projects);
      }
    }
  }, [isConnected, user]);

  // Socket utility functions
  const sendMessage = useCallback((chatId, message, projectId = null, messageType = 'text') => {
    if (!isConnected) {
      toast.error('Cannot send message: not connected to real-time services');
      return false;
    }
    chatHandlers.sendMessage(chatId, message, projectId, messageType);
    return true;
  }, [isConnected]);

  const joinChat = useCallback((chatId, projectId = null) => {
    if (!isConnected) {
      console.warn('Cannot join chat: not connected');
      return false;
    }
    chatHandlers.joinChat(chatId, projectId);
    return true;
  }, [isConnected]);

  const leaveChat = useCallback((chatId) => {
    if (!isConnected) {
      console.warn('Cannot leave chat: not connected');
      return false;
    }
    chatHandlers.leaveChat(chatId);
    return true;
  }, [isConnected]);

  const markMessagesRead = useCallback((chatId, messageIds) => {
    if (!isConnected) {
      console.warn('Cannot mark messages as read: not connected');
      return false;
    }
    chatHandlers.markMessagesRead(chatId, messageIds);
    return true;
  }, [isConnected]);

  const subscribeToProjectUpdates = useCallback((projectId) => {
    if (!isConnected) {
      console.warn('Cannot subscribe to project updates: not connected');
      return false;
    }
    projectHandlers.subscribeToProjectUpdates(projectId);
    return true;
  }, [isConnected]);

  const updateProjectStatus = useCallback((projectId, status, userId = null) => {
    if (!isConnected) {
      toast.error('Cannot update project status: not connected to real-time services');
      return false;
    }
    projectHandlers.updateProjectStatus(projectId, status, userId);
    return true;
  }, [isConnected]);

  // Check if user is online
  const isUserOnline = useCallback((userId) => {
    return onlineUsers.has(userId);
  }, [onlineUsers]);

  // Get online users list
  const getOnlineUsers = useCallback(() => {
    return Array.from(onlineUsers.values());
  }, [onlineUsers]);

  // Get connection info
  const getConnectionInfo = useCallback(() => {
    return {
      isConnected,
      status: connectionStatus,
      socketId: socketService.getSocket()?.id || null
    };
  }, [isConnected, connectionStatus]);

  const contextValue = {
    // Connection state
    isConnected,
    connectionStatus,
    
    // User status
    onlineUsers: Array.from(onlineUsers.values()),
    isUserOnline,
    getOnlineUsers,
    
    // Connection info
    getConnectionInfo,
    
    // Chat functions
    sendMessage,
    joinChat,
    leaveChat,
    markMessagesRead,
    
    // Project functions
    subscribeToProjectUpdates,
    updateProjectStatus,
    
    // Handlers for direct access
    chatHandlers,
    notificationHandlers,
    projectHandlers,
    
    // Socket service for advanced usage
    socketService,
    
    // Manual connection control
    initializeSocket,
    disconnectSocket
  };

  return (
    <SocketContext.Provider value={contextValue}>
      {children}
    </SocketContext.Provider>
  );
};

// Custom hook to use socket context
export const useSocket = () => {
  const context = useContext(SocketContext);
  if (!context) {
    throw new Error('useSocket must be used within a SocketProvider');
  }
  return context;
};

export default SocketContext;
