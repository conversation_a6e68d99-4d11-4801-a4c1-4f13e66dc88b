// Global provider for chat notifications
'use client';
import { createContext, useContext } from 'react';
import { AuthContext } from './AuthContext';
import { useChatNotifications } from '@/hooks/useChatNotifications';

export const ChatNotificationContext = createContext();

export const ChatNotificationProvider = ({ children }) => {
	const { user } = useContext(AuthContext);
	const { chatNotificationsEnabled, canReceiveNotifications } =
		useChatNotifications();

	return (
		<ChatNotificationContext.Provider value={{}}>
			{children}
		</ChatNotificationContext.Provider>
	);
};
