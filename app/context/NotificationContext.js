
'use client';
import { createContext, useState, useEffect, useCallback } from 'react';
import toast from 'react-hot-toast';

export const NotificationContext = createContext();

export const NotificationProvider = ({ children }) => {
	const [notificationStatus, setNotificationStatus] = useState('default');
	const [isSubscribed, setIsSubscribed] = useState(false);

	const requestNotificationPermission = useCallback(async () => {
		if ('Notification' in window) {
			try {
				const permission = await Notification.requestPermission();
				setNotificationStatus(permission);
				if (permission === 'granted') {
					subscribeUserToPush();
				} else {
					toast.error('Notification permission denied.');
				}
			} catch (error) {
				toast.error('Error requesting notification permission.');
				console.error('Notification permission error:', error);
			}
		} else {
			toast.error('This browser does not support desktop notification');
		}
	}, []);

	const subscribeUserToPush = useCallback(async () => {
		try {
			const registration = await navigator.serviceWorker.ready;
			const subscription = await registration.pushManager.subscribe({
				userVisibleOnly: true,
				applicationServerKey: process.env.NEXT_PUBLIC_VAPID_PUBLIC_KEY,
			});
			// Send subscription to backend
			// await fetchWithToken('/api/subscribe', {
			// 	method: 'POST',
			// 	body: JSON.stringify(subscription),
			// 	headers: {
			// 		'Content-Type': 'application/json',
			// 	},
			// });
			toast.success('Successfully subscribed to notifications!');
			setIsSubscribed(true);
		} catch (error) {
			toast.error('Failed to subscribe to notifications.');
			console.error('Push subscription error:', error);
		}
	}, []);

	useEffect(() => {
		if ('Notification' in window) {
			setNotificationStatus(Notification.permission);
		}
		if ('serviceWorker' in navigator && 'PushManager' in window) {
			navigator.serviceWorker.ready.then((registration) => {
				registration.pushManager.getSubscription().then((subscription) => {
					if (subscription) {
						setIsSubscribed(true);
					}
				});
			});
		}
	}, []);

	return (
		<NotificationContext.Provider
			value={{
				notificationStatus,
				isSubscribed,
				requestNotificationPermission,
				subscribeUserToPush,
			}}
		>
			{children}
		</NotificationContext.Provider>
	);
};

