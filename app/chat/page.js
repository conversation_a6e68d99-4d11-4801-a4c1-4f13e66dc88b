'use client';
import { useContext } from 'react';
import { AuthContext } from '@/app/context/AuthContext';

import { redirect } from 'next/navigation';
import UserChatList from '@/components/chat/UserChatList';
import Navbar from '@/components/Navbar';

export default function ChatsPage() {
	return (
		<>
			<Navbar />
			<div className='container mx-auto p-6'>
				<div className='max-w-4xl mx-auto'>
					<h1 className='text-3xl font-bold text-primary mb-6'>
						Project Chats
					</h1>
					<UserChatList />
				</div>
			</div>
		</>
	);
}
