'use client';
import { useState, useContext, useEffect, Suspense } from 'react';
import Link from 'next/link';
import { AuthContext } from '@/app/context/AuthContext';
import { useTheme } from '@/app/context/ThemeContext';
import { useAnalytics } from '@/app/context/AnalyticsContext';
import { useRouter } from 'next/navigation';
import usePageTracking from '@/app/hooks/usePageTracking';
import Navbar from '@/components/Navbar';
import { FaEye, FaEyeSlash } from 'react-icons/fa';
import { FcGoogle } from 'react-icons/fc';

const Login = () => {
	const { login, googleLogin } = useContext(AuthContext);
	const { theme } = useTheme();
	const { trackEvent } = useAnalytics();
	const [formData, setFormData] = useState({ email: '', password: '' });
	const [error, setError] = useState('');
	const [isLoading, setIsLoading] = useState(false);
	const [isGoogleLoading, setIsGoogleLoading] = useState(false);
	const router = useRouter();
	const [showPassword, setShowPassword] = useState(false);

	// Track page view
	usePageTracking('login_page');

	// Check if user exists in local storage and redirect
	useEffect(() => {
		const user = localStorage.getItem('user');
		if (user) {
			router.push('/'); // Redirect to homepage
		}

		// Check for error parameters in URL
		const urlParams = new URLSearchParams(window.location.search);
		const errorParam = urlParams.get('error');

		if (errorParam === 'google_callback_deprecated') {
			setError(
				'Google Sign-In has been updated. Please try signing in again using the button below.',
			);
			// Clean up the URL
			window.history.replaceState({}, document.title, window.location.pathname);
		}
	}, [router]);

	const togglePasswordVisibility = () => {
		setShowPassword((prev) => !prev);
	};

	const handleChange = (e) => {
		setFormData({ ...formData, [e.target.name]: e.target.value });
	};

	const handleSubmit = async (e) => {
		e.preventDefault();
		setIsLoading(true);
		setError('');

		// Track login attempt
		trackEvent('login_attempt', {
			method: 'email',
			email_domain: formData.email.split('@')[1],
		});

		try {
			await login(formData.email, formData.password);

			// Track successful login
			trackEvent('login_success', {
				method: 'email',
				email_domain: formData.email.split('@')[1],
			});

			router.push('/submit');
		} catch (error) {
			// Track login failure
			trackEvent('login_failure', {
				method: 'email',
				reason: error.message || 'Unknown error',
			});

			// Handle specific error cases from new backend
			if (error.needsVerification) {
				// Redirect to register page with email for verification
				router.push(
					`/auth/register?email=${encodeURIComponent(error.email)}&verify=true`,
				);
				return;
			}

			if (error.needsRegistration) {
				// Redirect to register page
				router.push('/auth/register');
				return;
			}

			setError(error.message || 'Login failed. Please try again.');
		} finally {
			setIsLoading(false);
		}
	};

	return (
		<div>
			<Suspense fallback={null}>
				<Navbar />
			</Suspense>
			<div className='min-h-screen flex items-center justify-center bg-background text-text p-6 '>
				<div className='backdrop-blur-md p-8 rounded-xl shadow-lg w-full max-w-md card'>
					<h2 className='text-3xl font-bold mb-6 text-center'>Login</h2>
					{error && (
						<div className='mb-4 p-3 bg-red-500 bg-opacity-30 border border-red-400 text-red-300 rounded-lg text-center'>
							{error}
						</div>
					)}
					<form
						onSubmit={handleSubmit}
						className='space-y-5'>
						<input
							type='email'
							name='email'
							placeholder='Email'
							value={formData.email}
							onChange={handleChange}
							className='w-full px-4 py-3 rounded-lg bg-background text-text border border-primary focus:outline-none focus:ring-2 focus:ring-primary'
							required
						/>
						<div className='relative'>
							<input
								type={showPassword ? 'text' : 'password'}
								name='password'
								placeholder='Password'
								value={formData.password}
								onChange={handleChange}
								className='w-full px-4 py-3 rounded-lg bg-background text-text border border-primary focus:outline-none focus:ring-2 focus:ring-primary'
								required
							/>
							<button
								type='button'
								onClick={togglePasswordVisibility}
								className='absolute inset-y-0 right-4 flex items-center text-text/50 hover:text-text/80'>
								{showPassword ? (
									<FaEyeSlash className='h-5 w-5' />
								) : (
									<FaEye className='h-5 w-5' />
								)}
							</button>
						</div>
						<button
							type='submit'
							disabled={isLoading}
							className='w-full bg-primary hover:opacity-90 text-white font-bold py-3 rounded-lg transition-all disabled:opacity-50 disabled:cursor-not-allowed'>
							{isLoading ? 'Logging in...' : 'Login'}
						</button>
					</form>

					<p className='text-center mt-6 text-text/80'>Or continue with</p>
					<button
						onClick={async () => {
							setIsGoogleLoading(true);
							setError('');

							// Track Google login attempt
							trackEvent('login_attempt', { method: 'google' });

							try {
								await googleLogin();

								// Track successful Google login
								trackEvent('login_success', { method: 'google' });

								router.push('/submit');
							} catch (error) {
								// Track Google login failure
								trackEvent('login_failure', {
									method: 'google',
									reason: error.message || 'Unknown error',
								});

								setError(
									error.message || 'Google login failed. Please try again.',
								);
							} finally {
								setIsGoogleLoading(false);
							}
						}}
						disabled={isGoogleLoading || isLoading}
						className='mt-4 flex items-center justify-center w-full py-3 bg-background border border-primary hover:bg-primary/5 rounded-lg transition-all disabled:opacity-50 disabled:cursor-not-allowed'>
						{isGoogleLoading ? (
							<div className='flex items-center space-x-2'>
								<div className='animate-spin rounded-full h-5 w-5 border-b-2 border-primary'></div>
								<span>Signing in...</span>
							</div>
						) : (
							<FcGoogle className='text-3xl' />
						)}
					</button>

					<p className='mt-4 text-center'>
						<Link
							href='/auth/forgetpassword'
							className='text-primary hover:underline'>
							Forgot Password?
						</Link>
					</p>
					<p className='mt-4 text-center text-text/80'>
						Don't have an account?{' '}
						<Link
							href='/auth/register'
							className='text-primary hover:underline'>
							Register
						</Link>
					</p>
				</div>
			</div>
		</div>
	);
};

// Wrap the entire component in Suspense for useSearchParams
const LoginPage = () => {
	return (
		<Suspense
			fallback={
				<div className='min-h-screen flex items-center justify-center bg-background'>
					<div className='animate-spin text-primary'>
						<svg
							className='w-10 h-10'
							xmlns='http://www.w3.org/2000/svg'
							fill='none'
							viewBox='0 0 24 24'>
							<circle
								className='opacity-25'
								cx='12'
								cy='12'
								r='10'
								stroke='currentColor'
								strokeWidth='4'></circle>
							<path
								className='opacity-75'
								fill='currentColor'
								d='M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z'></path>
						</svg>
					</div>
				</div>
			}>
			<Login />
		</Suspense>
	);
};

export default LoginPage;
