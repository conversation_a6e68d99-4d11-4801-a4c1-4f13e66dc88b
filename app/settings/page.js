'use client';
import { useContext, useEffect } from 'react';
import { AuthContext } from '../context/AuthContext';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import Navbar from '@/components/Navbar';
import ChatNotificationSettings from '@/components/chat/ChatNotificationSettings';
import NotificationBanner from '@/components/NotificationBanner';
import { NotificationContext } from '../context/NotificationContext';
import { FaBell, FaCog, FaUser } from 'react-icons/fa';

const SettingsPage = () => {
	const { user } = useContext(AuthContext);
	const { notificationStatus, requestNotificationPermission } =
		useContext(NotificationContext);

	useEffect(() => {
		if (!user) {
			window.location.href = '/auth/login';
		}
	}, [user]);

	if (!user) {
		return null;
	}

	return (
		<div className='min-h-screen bg-background'>
			<Navbar />
			<div className='max-w-4xl mx-auto p-6 space-y-8'>
				{/* Page Header */}
				<div className='text-center'>
					<h1 className='text-3xl font-bold text-primary mb-2'>Settings</h1>
					<p className='text-text/70'>
						Manage your account preferences and notifications
					</p>
				</div>

				{/* General Notification Banner */}
				{notificationStatus !== 'granted' && (
					<NotificationBanner
						notificationStatus={notificationStatus}
						requestNotificationPermission={requestNotificationPermission}
					/>
				)}

				{/* User Information */}
				<Card>
					<CardHeader>
						<CardTitle className='flex items-center gap-2 text-primary'>
							<FaUser />
							Account Information
						</CardTitle>
					</CardHeader>
					<CardContent className='space-y-4'>
						<div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
							<div>
								<label className='text-sm font-medium text-text/70'>Name</label>
								<p className='text-text font-medium'>{user.name}</p>
							</div>
							<div>
								<label className='text-sm font-medium text-text/70'>
									Email
								</label>
								<p className='text-text font-medium'>{user.email}</p>
							</div>
							{user.matricNumber && (
								<div>
									<label className='text-sm font-medium text-text/70'>
										Matric Number
									</label>
									<p className='text-text font-medium'>{user.matricNumber}</p>
								</div>
							)}
							<div>
								<label className='text-sm font-medium text-text/70'>
									Account Type
								</label>
								<p className='text-text font-medium'>
									{user.isAdmin ? 'Administrator' : 'Student'}
								</p>
							</div>
						</div>
					</CardContent>
				</Card>

				{/* Notification Settings */}
				<Card>
					<CardHeader>
						<CardTitle className='flex items-center gap-2 text-primary'>
							<FaBell />
							Notification Preferences
						</CardTitle>
					</CardHeader>
					<CardContent className='space-y-6'>
						{/* Browser Notifications Status */}
						<div>
							<h3 className='text-lg font-medium text-text mb-3'>
								Browser Notifications
							</h3>
							<div className='p-4 rounded-lg'>
								<div className='flex items-center justify-between'>
									<div>
										<p className='font-medium text-text'>
											Status:{' '}
											{notificationStatus === 'granted'
												? 'Enabled'
												: notificationStatus === 'denied'
												? 'Blocked'
												: 'Not Set'}
										</p>
										<p className='text-sm text-text/70'>
											{notificationStatus === 'granted'
												? 'You can receive browser notifications'
												: 'Enable to receive real-time notifications'}
										</p>
									</div>
									<div
										className={`w-3 h-3 rounded-full ${
											notificationStatus === 'granted'
												? 'bg-green-500'
												: notificationStatus === 'denied'
												? 'bg-red-500'
												: 'bg-yellow-500'
										}`}></div>
								</div>
							</div>
						</div>

						{/* Chat Notifications */}
						<div>
							<h3 className='text-lg font-medium text-text mb-3'>
								Chat Notifications
							</h3>
							<ChatNotificationSettings />
						</div>
					</CardContent>
				</Card>

				{/* Additional Settings */}
				<Card>
					<CardHeader>
						<CardTitle className='flex items-center gap-2 text-primary'>
							<FaCog />
							General Settings
						</CardTitle>
					</CardHeader>
					<CardContent>
						<div className='space-y-4'>
							<div className='flex items-center justify-between p-4 rounded-lg'>
								<div>
									<p className='font-medium text-text'>Email Notifications</p>
									<p className='text-sm text-text/70'>
										Receive important updates via email
									</p>
								</div>
								<label className='relative inline-flex items-center cursor-pointer'>
									<input
										type='checkbox'
										defaultChecked
										className='sr-only peer'
									/>
									<div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
								</label>
							</div>
						</div>
					</CardContent>
				</Card>
			</div>
		</div>
	);
};

export default SettingsPage;
