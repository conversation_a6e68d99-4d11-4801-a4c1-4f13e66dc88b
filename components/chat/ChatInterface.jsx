'use client';
import { useEffect, useState, useRef, useContext } from 'react';
import { AuthContext } from '@/app/context/AuthContext';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import {
	FaPaperPlane,
	FaUser,
	FaUserShield,
	FaSpinner,
	FaChevronDown,
	FaCheck,
} from 'react-icons/fa';
import toast from 'react-hot-toast';

const ChatInterface = ({ projectId, projectTitle, onClose }) => {
	const { user, fetchWithToken } = useContext(AuthContext);
	const [messages, setMessages] = useState([]);
	const [newMessage, setNewMessage] = useState('');
	const [loading, setLoading] = useState(true);
	const [sending, setSending] = useState(false);
	const [chatId, setChatId] = useState(null);
	const [isAtBottom, setIsAtBottom] = useState(true);
	const [chatParticipants, setChatParticipants] = useState([]); // Store chat participants

	const messagesEndRef = useRef(null);
	const scrollContainerRef = useRef(null);

	// Scroll to bottom of messages
	const scrollToBottom = () => {
		messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
	};

	// Track if user is scrolled near bottom
	const handleScroll = () => {
		if (!scrollContainerRef.current) return;

		const { scrollTop, scrollHeight, clientHeight } =
			scrollContainerRef.current;
		const distanceFromBottom = scrollHeight - scrollTop - clientHeight;
		setIsAtBottom(distanceFromBottom < 50);
	};

	useEffect(() => {
		if (isAtBottom) {
			scrollToBottom();
		}
	}, [messages, isAtBottom]);

	// Send notification to other chat participants
	const sendChatNotification = async (
		recipientId,
		senderName,
		messageContent,
	) => {
		try {
			const response = await fetchWithToken(
				`${process.env.NEXT_PUBLIC_BACKEND_URL}/api/notifications/send-notification`,
				{
					method: 'POST',
					headers: { 'Content-Type': 'application/json' },
					body: JSON.stringify({
						recipientUid: recipientId,
						senderName,
						messageContent: `New message in ${projectTitle}: "${
							messageContent.length > 50
								? messageContent.substring(0, 50) + '...'
								: messageContent
						}"`,
						notificationType: 'chat_message',
						projectId,
						chatId,
					}),
				},
			);

			if (!response.ok) {
				const errorData = await response.json();
				throw new Error(errorData.message || 'Failed to send notification');
			}

			console.log(`Chat notification sent successfully to user ${recipientId}`);
		} catch (error) {
			console.error('Error sending chat notification:', error);
			// Don't show error to user as this is not critical for chat functionality
		}
	};

	// Send notifications to all other participants in the chat
	const notifyOtherParticipants = async (messageContent) => {
		if (!chatParticipants.length || !user) return;

		// Get all participants except the current user
		const otherParticipants = chatParticipants.filter(
			(participant) => participant._id !== user._id,
		);

		// Send notification to each participant
		const notificationPromises = otherParticipants.map((participant) =>
			sendChatNotification(participant._id, user.name, messageContent),
		);

		// Execute all notifications concurrently
		await Promise.allSettled(notificationPromises);
	};

	// Initialize chat and fetch messages
	useEffect(() => {
		const initializeChat = async () => {
			try {
				const chatResponse = await fetchWithToken(
					`${process.env.NEXT_PUBLIC_BACKEND_URL}/api/projects/${projectId}/chat`,
				);

				if (chatResponse.ok) {
					const chatData = await chatResponse.json();
					setChatId(chatData.chat._id);

					// Set chat participants if available
					if (chatData.chat.participants) {
						setChatParticipants(chatData.chat.participants);
					}

					const messagesResponse = await fetchWithToken(
						`${process.env.NEXT_PUBLIC_BACKEND_URL}/api/projects/chat/${chatData.chat._id}/messages`,
					);

					if (messagesResponse.ok) {
						const messagesData = await messagesResponse.json();
						const initialMessages = messagesData.messages || [];
						setMessages(initialMessages);

						// If participants not available from chat data, extract from messages
						if (!chatData.chat.participants && initialMessages.length > 0) {
							const uniqueParticipants = [];
							const participantIds = new Set();

							initialMessages.forEach((message) => {
								if (!participantIds.has(message.sender._id)) {
									participantIds.add(message.sender._id);
									uniqueParticipants.push(message.sender);
								}
							});

							setChatParticipants(uniqueParticipants);
						}
					}
				}
			} catch (error) {
				console.error('Error initializing chat:', error);
				toast.error('Failed to load chat');
			} finally {
				setLoading(false);
			}
		};

		if (projectId) {
			initializeChat();
		}
	}, [projectId, fetchWithToken]);

	// Send message
	const handleSendMessage = async () => {
		if (!newMessage.trim() || !chatId || sending) return;

		setSending(true);
		try {
			const response = await fetchWithToken(
				`${process.env.NEXT_PUBLIC_BACKEND_URL}/api/projects/chat/${chatId}/message`,
				{
					method: 'POST',
					headers: { 'Content-Type': 'application/json' },
					body: JSON.stringify({
						content: newMessage.trim(),
						messageType: 'text',
					}),
				},
			);

			if (response.ok) {
				const messageData = await response.json();
				const newMessageData = messageData.data;
				setMessages((prev) => [...prev, newMessageData]);

				// Send notifications to other participants
				await notifyOtherParticipants(newMessage.trim());

				setNewMessage('');
				toast.success('Message sent');
			} else {
				throw new Error('Failed to send message');
			}
		} catch (error) {
			console.error('Error sending message:', error);
			toast.error('Failed to send message');
		} finally {
			setSending(false);
		}
	};

	// Fetch latest messages
	const fetchMessages = async () => {
		if (!chatId) return;
		try {
			const response = await fetchWithToken(
				`${process.env.NEXT_PUBLIC_BACKEND_URL}/api/projects/chat/${chatId}/messages`,
			);
			if (response.ok) {
				const messagesData = await response.json();
				const updatedMessages = messagesData.messages || [];
				setMessages(updatedMessages);
			}
		} catch (err) {
			console.error('Error fetching new messages:', err);
		}
	};

	// Auto refresh messages every 30 seconds
	useEffect(() => {
		if (!chatId) return;

		const interval = setInterval(() => {
			fetchMessages();
		}, 30000); // 30 seconds

		return () => clearInterval(interval);
	}, [chatId]);

	// Handle key press
	const handleKeyDown = (e) => {
		if (e.key === 'Enter' && !e.shiftKey) {
			e.preventDefault();
			handleSendMessage();
		}
	};

	// Format message timestamp
	const formatTimestamp = (timestamp) => {
		return new Date(timestamp).toLocaleString('en-US', {
			month: 'short',
			day: 'numeric',
			hour: '2-digit',
			minute: '2-digit',
		});
	};

	// Get user badge
	const getUserBadge = (sender) => {
		if (sender.isAdmin) {
			return (
				<div className='flex items-center gap-1 bg-gradient-to-r from-primary to-accent text-white text-xs font-bold py-1 px-2 rounded-full'>
					<FaUserShield className='text-xs' />
					<span>Vendor</span>
				</div>
			);
		}
		return null;
	};

	// Get read status for own messages
	const getReadStatus = (message) => {
		if (message.sender._id !== user?._id) return null; // Only show for own messages
		if (message.messageType === 'system') return null; // Don't show for system messages

		const readByOthers =
			message.readBy?.filter((read) => read.userId !== user?._id) || [];

		if (readByOthers.length > 0) {
			return (
				<div className='flex items-center gap-1 text-xs text-white/70 mt-1'>
					<FaCheck className='text-xs' />
					<span>Read</span>
				</div>
			);
		} else {
			return (
				<div className='flex items-center gap-1 text-xs text-white/70 mt-1'>
					<FaCheck className='text-xs' />
					<span>Delivered</span>
				</div>
			);
		}
	};

	if (loading) {
		return (
			<div className='flex justify-center items-center h-96'>
				<FaSpinner className='animate-spin text-4xl text-primary' />
			</div>
		);
	}

	return (
		<Card className='h-full flex flex-col bg-background border border-primary/20 relative'>
			{/* Chat Header */}
			<CardHeader className='border-b border-primary/20 pb-4'>
				<div className='flex items-center justify-between'>
					<div>
						<CardTitle className='text-primary'>{projectTitle}</CardTitle>
						<p className='text-sm text-text/70 mt-1'>
							Project Chat{' '}
							{chatParticipants.length > 0 &&
								`• ${chatParticipants.length} participants`}
						</p>
					</div>
				</div>
			</CardHeader>

			{/* Messages Area */}
			<CardContent className='flex-1 flex flex-col p-0 relative'>
				<div
					ref={scrollContainerRef}
					onScroll={handleScroll}
					className='flex-1 overflow-y-auto p-4 space-y-4 max-h-96'
					style={{ scrollbarWidth: 'none', msOverflowStyle: 'none' }}>
					<style jsx>{`
						div::-webkit-scrollbar {
							display: none;
						}
					`}</style>

					{messages.length === 0 ? (
						<div className='text-center text-text/50 py-8'>
							<p>No messages yet. Start the conversation!</p>
						</div>
					) : (
						messages.map((message) => {
							const isOwn = message.sender._id === user?._id;
							const isSystem = message.messageType === 'system';

							// System messages display
							if (isSystem) {
								return (
									<div
										key={message._id}
										className='flex justify-center'>
										<div className='bg-gray-100 text-gray-600 text-xs px-3 py-2 rounded-full border'>
											{message.content}
										</div>
									</div>
								);
							}

							return (
								<div
									key={message._id}
									className={`flex ${isOwn ? 'justify-end' : 'justify-start'}`}>
									<div
										className={`max-w-xs lg:max-w-md px-4 py-3 rounded-lg ${
											isOwn
												? 'bg-gradient-to-r from-primary to-accent text-white'
												: 'bg-primary/10 text-text border border-primary/20'
										}`}>
										{!isOwn && (
											<div className='flex items-center gap-2 mb-2'>
												<FaUser className='text-primary text-sm' />
												<span className='font-medium text-sm text-primary'>
													{message.sender.name}
												</span>
												{getUserBadge(message.sender)}
											</div>
										)}

										<p className='text-sm mb-1'>{message.content}</p>

										<div className='flex items-center justify-between'>
											<p
												className={`text-xs ${
													isOwn ? 'text-white/70' : 'text-text/50'
												}`}>
												{formatTimestamp(message.createdAt)}
											</p>
											{getReadStatus(message)}
										</div>
									</div>
								</div>
							);
						})
					)}
					<div ref={messagesEndRef} />
				</div>

				{/* Scroll to Bottom Button - Better positioned */}
				{!isAtBottom && (
					<div className='absolute bottom-24 left-1/2 transform -translate-x-1/2 z-10'>
						<Button
							onClick={scrollToBottom}
							className='bg-white border border-primary/20 text-primary hover:bg-primary hover:text-white shadow-lg rounded-full p-2 transition-all duration-200 hover:scale-105'>
							<FaChevronDown className='text-sm' />
						</Button>
					</div>
				)}

				{/* Input Area */}
				<div className='border-t border-primary/20 p-4'>
					<div className='flex gap-2'>
						<div className='flex-1 relative'>
							<textarea
								value={newMessage}
								onChange={(e) => setNewMessage(e.target.value)}
								onKeyPress={handleKeyDown}
								placeholder='Type your message...'
								className='w-full p-3 border border-primary/30 rounded-lg focus:ring-2 focus:ring-accent bg-background text-text resize-none'
								rows={2}
								disabled={sending}
							/>
						</div>
						<Button
							onClick={handleSendMessage}
							disabled={!newMessage.trim() || sending}
							className='bg-gradient-to-r from-primary to-accent text-white hover:opacity-90 btn-3d'>
							{sending ? (
								<FaSpinner className='animate-spin text-white' />
							) : (
								<FaPaperPlane className='text-white' />
							)}
						</Button>
					</div>
				</div>
			</CardContent>
		</Card>
	);
};

export default ChatInterface;
