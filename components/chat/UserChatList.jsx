'use client';
import { useState, useEffect, useContext } from 'react';
import { AuthContext } from '@/app/context/AuthContext';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { <PERSON>a<PERSON><PERSON><PERSON>, <PERSON>a<PERSON><PERSON><PERSON>, FaClock } from 'react-icons/fa';
import ChatModal from './ChatModal';
import toast from 'react-hot-toast';

const UserChatList = () => {
	const { user, fetchWithToken } = useContext(AuthContext);
	const [chats, setChats] = useState([]);
	const [loading, setLoading] = useState(true);
	const [selectedChat, setSelectedChat] = useState(null);

	useEffect(() => {
		const fetchUserChats = async () => {
			try {
				const response = await fetchWithToken(
					`${process.env.NEXT_PUBLIC_BACKEND_URL}/api/projects/user/chats`,
				);

				if (response.ok) {
					const data = await response.json();
					setChats(data.chats || []);
				} else {
					throw new Error('Failed to fetch chats');
				}
			} catch (error) {
				console.error('Error fetching chats:', error);
				toast.error('Failed to load chats');
			} finally {
				setLoading(false);
			}
		};

		if (user) {
			fetchUserChats();
		}
	}, [user, fetchWithToken]);

	const formatLastActivity = (timestamp) => {
		const date = new Date(timestamp);
		const now = new Date();
		const diffTime = Math.abs(now - date);
		const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

		if (diffDays === 1) return 'Today';
		if (diffDays === 2) return 'Yesterday';
		if (diffDays <= 7) return `${diffDays} days ago`;
		return date.toLocaleDateString();
	};

	if (loading) {
		return (
			<Card className='card'>
				<CardContent className='flex justify-center items-center h-64'>
					<FaSpinner className='animate-spin text-4xl text-primary' />
				</CardContent>
			</Card>
		);
	}

	return (
		<>
			<Card className='card'>
				<CardHeader>
					<CardTitle className='flex items-center gap-2 text-primary'>
						<FaComments />
						My Project Chats
					</CardTitle>
				</CardHeader>
				<CardContent>
					{chats.length === 0 ? (
						<div className='text-center py-8 text-text/50'>
							<FaComments className='mx-auto text-4xl mb-4 text-primary/30' />
							<p>No project chats yet</p>
							<p className='text-sm mt-2'>
								Submit a project to start chatting with admins
							</p>
						</div>
					) : (
						<div className='space-y-3'>
							{chats.map((chat) => (
								<div
									key={chat._id}
									onClick={() => setSelectedChat(chat)}
									className='p-4 border border-primary/20 rounded-lg hover:bg-primary/5 cursor-pointer transition-all hover:shadow-md'>
									<div className='flex items-start justify-between'>
										<div className='flex-1'>
											<h3 className='font-medium text-text mb-1'>
												{chat.projectTitle}
											</h3>
											{chat.lastMessage && (
												<p className='text-sm text-text/70 mb-2 line-clamp-2'>
													{chat.lastMessage.content}
												</p>
											)}
											<div className='flex items-center gap-2 text-xs text-text/50'>
												<FaClock />
												<span>
													{chat.lastMessage
														? formatLastActivity(chat.lastMessage.createdAt)
														: 'No messages yet'}
												</span>
											</div>
										</div>
										{chat.unreadCount > 0 && (
											<div className='bg-accent text-white text-xs rounded-full w-6 h-6 flex items-center justify-center'>
												{chat.unreadCount}
											</div>
										)}
									</div>
								</div>
							))}
						</div>
					)}
				</CardContent>
			</Card>

			{selectedChat && (
				<ChatModal
					isOpen={!!selectedChat}
					onClose={() => setSelectedChat(null)}
					projectId={selectedChat.projectId}
					projectTitle={selectedChat.projectTitle}
				/>
			)}
		</>
	);
};

export default UserChatList;
