'use client';
import { Dialog, DialogContent, DialogTitle } from '@/components/ui/dialog';
import ChatInterface from './ChatInterface';

const ChatModal = ({ isOpen, onClose, projectId, projectTitle }) => {
	return (
		<Dialog
			open={isOpen}
			onOpenChange={onClose}>
			<DialogContent className='max-w-2xl h-[80vh] p-0'>
				<div className='hidden'>
					<DialogTitle>Chat for {projectTitle || 'Project'}</DialogTitle>
				</div>

				<ChatInterface
					projectId={projectId}
					projectTitle={projectTitle}
					onClose={onClose}
				/>
			</DialogContent>
		</Dialog>
	);
};

export default ChatModal;
