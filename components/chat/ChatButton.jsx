'use client';
import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { FaComments } from 'react-icons/fa';
import ChatModal from './ChatModal';

const ChatButton = ({ projectId, projectTitle, className = '' }) => {
	const [isOpen, setIsOpen] = useState(false);

	return (
		<>
			<Button
				onClick={() => setIsOpen(true)}
				className={`flex items-center gap-2 bg-gradient-to-r from-primary to-accent text-white hover:opacity-90 btn-3d ${className}`}
				aria-label='Open project chat'>
				<FaComments className='text-white' />
				<span className='hidden sm:inline text-white'>Chat</span>
			</Button>

			<ChatModal
				isOpen={isOpen}
				onClose={() => setIsOpen(false)}
				projectId={projectId}
				projectTitle={projectTitle}
			/>
		</>
	);
};

export default ChatButton;
