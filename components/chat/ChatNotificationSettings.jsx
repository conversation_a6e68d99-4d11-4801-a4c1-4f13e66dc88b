'use client';
import { useContext } from 'react';
import { NotificationContext } from '@/app/context/NotificationContext';
import { useChatNotifications } from '@/hooks/useChatNotifications';
import { FaBell, FaBellSlash, FaInfoCircle } from 'react-icons/fa';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';

const ChatNotificationSettings = () => {
	const { notificationStatus, requestNotificationPermission } = useContext(NotificationContext);
	const { chatNotificationsEnabled, toggleChatNotifications, canReceiveNotifications } = useChatNotifications();

	if (!('Notification' in window)) {
		return (
			<Card className="bg-orange-50 border-orange-200">
				<CardContent className="p-4">
					<div className="flex items-center gap-3">
						<FaInfoCircle className="text-orange-500 text-xl" />
						<div>
							<p className="font-medium text-orange-800">Browser Not Supported</p>
							<p className="text-sm text-orange-600">
								Your browser doesn't support push notifications.
							</p>
						</div>
					</div>
				</CardContent>
			</Card>
		);
	}

	if (notificationStatus === 'denied') {
		return (
			<Card className="bg-red-50 border-red-200">
				<CardContent className="p-4">
					<div className="flex items-center gap-3">
						<FaBellSlash className="text-red-500 text-xl" />
						<div className="flex-1">
							<p className="font-medium text-red-800">Notifications Blocked</p>
							<p className="text-sm text-red-600">
								You've blocked notifications. Enable them in your browser settings to receive chat alerts.
							</p>
						</div>
					</div>
				</CardContent>
			</Card>
		);
	}

	if (notificationStatus === 'default') {
		return (
			<Card className="bg-blue-50 border-blue-200">
				<CardContent className="p-4">
					<div className="flex items-center justify-between">
						<div className="flex items-center gap-3">
							<FaBell className="text-blue-500 text-xl" />
							<div>
								<p className="font-medium text-blue-800">Enable Chat Notifications</p>
								<p className="text-sm text-blue-600">
									Get notified when you receive new chat messages
								</p>
							</div>
						</div>
						<Button
							onClick={requestNotificationPermission}
							className="bg-blue-500 hover:bg-blue-600 text-white"
						>
							Enable
						</Button>
					</div>
				</CardContent>
			</Card>
		);
	}

	// Notifications are granted
	return (
		<Card className="bg-green-50 border-green-200">
			<CardContent className="p-4">
				<div className="flex items-center justify-between">
					<div className="flex items-center gap-3">
						<FaBell className={`text-xl ${chatNotificationsEnabled ? 'text-green-500' : 'text-gray-400'}`} />
						<div>
							<p className="font-medium text-green-800">Chat Notifications</p>
							<p className="text-sm text-green-600">
								{chatNotificationsEnabled 
									? 'You\'ll receive notifications for new chat messages'
									: 'Chat notifications are currently disabled'
								}
							</p>
						</div>
					</div>
					<Button
						onClick={() => toggleChatNotifications(!chatNotificationsEnabled)}
						className={`${
							chatNotificationsEnabled 
								? 'bg-red-500 hover:bg-red-600' 
								: 'bg-green-500 hover:bg-green-600'
						} text-white`}
					>
						{chatNotificationsEnabled ? 'Disable' : 'Enable'}
					</Button>
				</div>
			</CardContent>
		</Card>
	);
};

export default ChatNotificationSettings;
