'use client';
import React, { useState, useEffect, useRef } from 'react';
import { useSocket } from '../../app/context/SocketContext';
import { useChat } from '../../hooks/useChat';
import { Card, CardHeader, CardTitle, CardContent } from '../ui/card';
import { Button } from '../ui/button';
import toast from 'react-hot-toast';

const SocketChatExample = ({ chatId, projectId, projectTitle = 'Example Project' }) => {
  const { isConnected, connectionStatus } = useSocket();
  
  // Use the custom chat hook
  const {
    messages,
    typingUsers,
    participants,
    isJoined,
    sendMessage,
    markMessagesRead,
    startTyping,
    stopTyping,
    getUnreadCount,
    loading
  } = useChat(chatId, projectId);

  const [newMessage, setNewMessage] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const messagesEndRef = useRef(null);
  const typingTimeoutRef = useRef(null);

  // Auto scroll to bottom when new messages arrive
  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const handleSendMessage = (e) => {
    e.preventDefault();
    if (!newMessage.trim() || !isConnected) return;

    const success = sendMessage(newMessage.trim());
    if (success) {
      setNewMessage('');
      stopTyping();
      setIsTyping(false);
    }
  };

  const handleInputChange = (e) => {
    setNewMessage(e.target.value);
    
    // Handle typing indicators
    if (!isTyping) {
      startTyping();
      setIsTyping(true);
    }

    // Clear existing timeout
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }

    // Stop typing after 2 seconds of inactivity
    typingTimeoutRef.current = setTimeout(() => {
      stopTyping();
      setIsTyping(false);
    }, 2000);
  };

  const handleMarkAsRead = () => {
    const unreadMessages = messages
      .filter(msg => !msg.read)
      .map(msg => msg._id || msg.id);
    
    if (unreadMessages.length > 0) {
      markMessagesRead(unreadMessages);
    }
  };

  const formatTime = (timestamp) => {
    return new Date(timestamp).toLocaleTimeString([], { 
      hour: '2-digit', 
      minute: '2-digit' 
    });
  };

  if (!chatId) {
    return (
      <Card className="w-full max-w-2xl mx-auto">
        <CardContent className="p-6 text-center">
          <p className="text-gray-500">Please provide a chatId to start chatting</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span>{projectTitle} - Chat</span>
          <div className="flex items-center space-x-2 text-sm">
            <div className={`w-2 h-2 rounded-full ${
              isConnected ? 'bg-green-500' : 'bg-red-500'
            }`} />
            <span className="text-gray-600">
              {isConnected ? 'Connected' : 'Disconnected'}
            </span>
            {getUnreadCount() > 0 && (
              <Button 
                size="sm" 
                variant="outline" 
                onClick={handleMarkAsRead}
                className="ml-2"
              >
                Mark {getUnreadCount()} as read
              </Button>
            )}
          </div>
        </CardTitle>
      </CardHeader>

      <CardContent>
        {/* Connection Status */}
        {!isConnected && (
          <div className="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded mb-4">
            <strong>Warning:</strong> You are not connected to real-time services. 
            Status: {connectionStatus}
          </div>
        )}

        {/* Chat Messages */}
        <div className="h-96 overflow-y-auto border rounded-lg p-4 mb-4 bg-gray-50">
          {loading ? (
            <div className="flex justify-center items-center h-full">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            </div>
          ) : messages.length > 0 ? (
            <div className="space-y-3">
              {messages.map((message, index) => (
                <div
                  key={message._id || message.id || index}
                  className={`flex ${
                    message.senderId === 'current-user-id' ? 'justify-end' : 'justify-start'
                  }`}
                >
                  <div
                    className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
                      message.senderId === 'current-user-id'
                        ? 'bg-blue-600 text-white'
                        : 'bg-white border'
                    }`}
                  >
                    <p className="text-sm">{message.message || message.content}</p>
                    <div className="flex items-center justify-between mt-1">
                      <span className="text-xs opacity-70">
                        {formatTime(message.timestamp || message.createdAt)}
                      </span>
                      {!message.read && message.senderId !== 'current-user-id' && (
                        <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                      )}
                    </div>
                  </div>
                </div>
              ))}
              
              {/* Typing Indicators */}
              {typingUsers.length > 0 && (
                <div className="flex justify-start">
                  <div className="bg-gray-200 px-4 py-2 rounded-lg max-w-xs">
                    <div className="flex space-x-1">
                      <div className="w-2 h-2 bg-gray-500 rounded-full animate-bounce"></div>
                      <div className="w-2 h-2 bg-gray-500 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                      <div className="w-2 h-2 bg-gray-500 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                    </div>
                    <span className="text-xs text-gray-600 ml-1">
                      {typingUsers.length === 1 ? 'Someone is typing...' : `${typingUsers.length} people are typing...`}
                    </span>
                  </div>
                </div>
              )}
              
              <div ref={messagesEndRef} />
            </div>
          ) : (
            <div className="flex justify-center items-center h-full text-gray-500">
              No messages yet. Start the conversation!
            </div>
          )}
        </div>

        {/* Message Input */}
        <form onSubmit={handleSendMessage} className="flex space-x-2">
          <input
            type="text"
            value={newMessage}
            onChange={handleInputChange}
            placeholder={isConnected ? "Type your message..." : "Connecting..."}
            disabled={!isConnected || !isJoined}
            className="flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:bg-gray-100"
          />
          <Button
            type="submit"
            disabled={!newMessage.trim() || !isConnected || !isJoined}
            className="px-6"
          >
            Send
          </Button>
        </form>

        {/* Chat Info */}
        <div className="mt-4 text-sm text-gray-600">
          <div className="flex flex-wrap gap-4">
            <span>Chat ID: {chatId}</span>
            {projectId && <span>Project ID: {projectId}</span>}
            <span>Joined: {isJoined ? 'Yes' : 'No'}</span>
            <span>Participants: {participants.length}</span>
            {getUnreadCount() > 0 && (
              <span className="text-blue-600 font-medium">
                Unread: {getUnreadCount()}
              </span>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default SocketChatExample;
