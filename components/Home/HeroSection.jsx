import AnimatedUploadProgress from '@/components/AnimatedUploadProgress';
import Underline from '@/components/Underline';
import { Check, Shield, Sparkles, Upload } from 'lucide-react';
import Image from 'next/image';
import Link from 'next/link';
import { FaUpload, FaArrowRight, FaCheckCircle } from 'react-icons/fa';

const HeroSection = () => {
	return (
		<section
			className='relative overflow-hidden py-24 md:py-32 px-6'
			aria-labelledby='hero-heading'>
			{/* Background elements */}
			<div
				className='absolute inset-0 opacity-20'
				aria-hidden='true'>
				<div className='absolute top-20 left-10 w-40 h-40 rounded-full bg-primary blur-3xl'></div>
				<div className='absolute bottom-10 right-10 w-60 h-60 rounded-full bg-secondary blur-3xl'></div>
			</div>
			<div className='flex justify-center mb-8 cursor-pointer'>
				<Link
					href='https://www.producthunt.com/products/uploaddoc?embed=true&utm_source=badge-featured&utm_medium=badge&utm_source=badge-uploaddoc'
					target='_blank'
					rel='noopener noreferrer'>
					<Image
						src='https://api.producthunt.com/widgets/embed-image/v1/featured.svg?post_id=997847&theme=light&t=1753528833939'
						alt='UploadDoc - Document management platform | Product Hunt'
						width={250}
						height={54}
						className='hover:opacity-90 transition-opacity duration-200 cursor-pointer'
					/>
				</Link>
			</div>
			<div className='relative z-10 max-w-7xl mx-auto'>
				<div className='flex flex-col lg:flex-row items-center'>
					<header className='lg:w-1/2 text-center lg:text-left mb-12 lg:mb-0'>
						<div className='inline-block p-[1px] rounded-full text-blue-700 text-sm font-medium mb-6 animate-fade-in font-work-sans bg-gradient-to-r from-blue-300 to-purple-300 shadow-pink-200 shadow-sm'>
							<span className='flex items-center gap-2 w-full h-full bg-blue-100 px-4 py-1.5 rounded-full font-medium'>
								<Sparkles
									className='h-4 w-4 text-blue-900'
									aria-hidden='true'
								/>
								Simplify Your Document Management{' '}
							</span>
						</div>

						<h1
							id='hero-heading'
							className='text-4xl md:text-5xl lg:text-6xl font-bold mb-6 text-text leading-tight'>
							Centralized platform for effortless document handling.{' '}
							<span className='text-transparent bg-clip-text bg-gradient-to-r from-primary to-accent'>
								Without the Hassle
							</span>
						</h1>
						<span className='block w-full max-w-[200px] sm:max-w-[300px] mx-auto lg:mx-0'>
							<Underline
								width='253'
								height='20'
							/>
						</span>

						<p className='text-lg md:text-xl text-text/80 max-w-xl mb-8 leading-relaxed'>
							Replace WhatsApp file sharing with our secure, dedicated platform
							for seamless document submission and printing.
						</p>

						<div className='flex flex-wrap items-center justify-center lg:justify-start gap-4'>
							<Link
								href='/submit'
								className='btn-3d bg-gradient-to-r from-primary to-accent text-white px-8 py-4 rounded-lg shadow-lg hover:opacity-90 transition-all duration-300 transform hover:scale-105 font-medium flex items-center gap-2'>
								<span>Submit Documents</span>
								<FaArrowRight />
							</Link>

							<Link
								href='/download'
								className='btn-3d bg-gradient-to-r from-accent to-secondary text-white px-8 py-4 rounded-lg shadow-lg hover:opacity-90 transition-all duration-300 transform hover:scale-105 font-medium flex items-center gap-2'>
								<span>Download App</span>
								<FaArrowRight />
							</Link>

							<Link
								href='/upgradetoadmin'
								title='Become a vendor for free'
								className='glow-effect text-text border border-primary/30 bg-primary/10 hover:bg-primary/20 px-6 py-4 rounded-lg transition-all duration-300 font-medium'>
								Become a Vendor for free
							</Link>
						</div>

						<div className='mt-8 grid grid-cols-3 gap-4'>
							<div className='flex items-center gap-2 text-text/80'>
								<FaCheckCircle className='text-primary' />
								<span>Secure</span>
							</div>
							<div className='flex items-center gap-2 text-text/80'>
								<FaCheckCircle className='text-primary' />
								<span>Fast</span>
							</div>
							<div className='flex items-center gap-2 text-text/80'>
								<FaCheckCircle className='text-primary' />
								<span>Simple</span>
							</div>
						</div>
					</header>

					<div className='lg:w-1/2 flex justify-center'>
						<div className='card-3d w-full max-w-md rounded-lg p-6'>
							<div className='flex justify-center mb-4'>
								<div className='btn-3d bg-gradient-to-r from-primary to-accent p-4 rounded-full'>
									<FaUpload className='w-5 h-5 text-white' />
								</div>
							</div>

							<h2 className='text-xl font-bold text-center mb-1 text-text'>
								Upload Documents
							</h2>
							<p className='text-text/70 text-center mb-6'>
								Secure and fast document processing
							</p>

							<div className='space-y-4'>
								{/* Completed upload */}
								<div className='card bg-primary/10 p-4 rounded-lg flex items-center'>
									<div className='btn-3d bg-gradient-to-r from-primary to-accent p-2 rounded-full mr-4'>
										<Check className='w-5 h-5 text-white' />
									</div>
									<div>
										<p className='font-medium text-text'>Report-Final.pdf</p>
										<p className='text-text/70 text-sm'>
											Uploaded successfully
										</p>
									</div>
								</div>

								{/* In progress upload */}
								<AnimatedUploadProgress
									fileName='Final Project.pdf'
									initialProgress={10}
								/>

								{/* Security info */}
								<div className='card bg-primary/10 p-4 rounded-lg flex items-center'>
									<div className='btn-3d bg-gradient-to-r from-primary to-accent p-2 rounded-full mr-4'>
										<Shield className='w-5 h-5 text-white' />
									</div>
									<div>
										<p className='font-medium text-text'>Secure Storage</p>
										<p className='text-text/70 text-sm'>
											Accessible only by authorized admins.
										</p>
									</div>
								</div>
							</div>

							{/* Upload button */}
							<Link
								href='/submit'
								className='w-full bg-gradient-to-r from-primary to-accent text-white rounded-lg py-3 mt-6 flex items-center justify-center hover:opacity-90 transition-all duration-300'>
								<Upload className='w-5 h-5 mr-2' />
								Upload New Document
							</Link>
						</div>
					</div>
				</div>
			</div>
		</section>
	);
};

export default HeroSection;
