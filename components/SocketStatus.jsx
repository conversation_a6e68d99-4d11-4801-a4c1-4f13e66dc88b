'use client';
import React from 'react';
import { useSocket } from '../app/context/SocketContext';

const SocketStatus = ({ className = '' }) => {
  const { isConnected, connectionStatus, getConnectionInfo } = useSocket();
  
  const getStatusColor = () => {
    switch (connectionStatus) {
      case 'connected':
        return 'bg-green-500';
      case 'connecting':
        return 'bg-yellow-500';
      case 'disconnected':
        return 'bg-gray-500';
      case 'error':
      case 'auth_error':
        return 'bg-red-500';
      default:
        return 'bg-gray-500';
    }
  };

  const getStatusText = () => {
    switch (connectionStatus) {
      case 'connected':
        return 'Real-time services connected';
      case 'connecting':
        return 'Connecting to real-time services...';
      case 'disconnected':
        return 'Disconnected from real-time services';
      case 'error':
        return 'Connection error';
      case 'auth_error':
        return 'Authentication error';
      default:
        return 'Unknown status';
    }
  };

  const getStatusIcon = () => {
    switch (connectionStatus) {
      case 'connected':
        return '🟢';
      case 'connecting':
        return '🟡';
      case 'disconnected':
        return '⚫';
      case 'error':
      case 'auth_error':
        return '🔴';
      default:
        return '⚫';
    }
  };

  return (
    <div className={`flex items-center space-x-2 ${className}`}>
      <div className="flex items-center space-x-2">
        <div 
          className={`w-2 h-2 rounded-full ${getStatusColor()} ${
            connectionStatus === 'connecting' ? 'animate-pulse' : ''
          }`}
          title={getStatusText()}
        />
        <span className="text-xs text-gray-600 dark:text-gray-400">
          {getStatusIcon()}
        </span>
      </div>
    </div>
  );
};

export default SocketStatus;
