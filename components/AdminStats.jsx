import Link from 'next/link';
import { useState, useEffect } from 'react';
import {
	FaBell,
	FaBellSlash,
	FaCoins,
	FaFileAlt,
	FaExclamationCircle,
	FaUsers,
} from 'react-icons/fa';
import { useData } from '@/app/context/DataContext';
import Pagination from '@/components/Pagination';

const AdminStats = ({
	user,
	isSubscribed,
	handleUnsubscribe,
	registerServiceWorker,
	getLoggedInAdminProjectCount,
}) => {
	const { adminUsers, pagination, updatePagination, loading } = useData();
	const [admins, setAdmins] = useState([]);
	const [adminPagination, setAdminPagination] = useState({
		currentPage: 1,
		limit: 10,
		totalPages: 1,
		totalCount: 0,
	});

	// Effect to update local state when adminUsers changes
	useEffect(() => {
		if (adminUsers && Array.isArray(adminUsers)) {
			setAdmins(adminUsers);
		}

		if (pagination && pagination.admins) {
			setAdminPagination(pagination.admins);
		}
	}, [adminUsers, pagination]);

	// Initial fetch of admin users if not already loaded
	useEffect(() => {
		if (user?.superAdmin && (!adminUsers || adminUsers.length === 0)) {
			// Force refresh of admin users
			updatePagination('admins', { currentPage: 1, limit: 10 });
		}
	}, [user, adminUsers, updatePagination]);

	// Handle page change
	const handlePageChange = (page) => {
		updatePagination('admins', { currentPage: page });
	};

	// Handle limit change
	const handleLimitChange = (limit) => {
		updatePagination('admins', { limit, currentPage: 1 });
	};

	return (
		<div className='w-full'>
			{/* Main Grid Layout */}
			<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4'>
				{/* Notifications Card */}
				{user?.isAdmin && (
					<div className='bg-background border border-primary/20 rounded-lg p-4 flex items-center justify-between card'>
						<div className='flex items-center gap-4'>
							<div
								className={`p-3 rounded-full ${
									isSubscribed
										? 'bg-primary/10'
										: 'bg-gray-100 dark:bg-gray-800'
								}`}>
								{isSubscribed ? (
									<FaBell className='w-5 h-5 text-accent' />
								) : (
									<FaBellSlash className='w-5 h-5 text-gray-600 dark:text-gray-400' />
								)}
							</div>
							<div>
								<h3 className='font-medium text-text'>Notifications</h3>
								<p className='text-sm text-text/70'>
									{isSubscribed ? 'Receiving updates' : 'Updates disabled'}
								</p>
							</div>
						</div>
						<button
							onClick={isSubscribed ? handleUnsubscribe : registerServiceWorker}
							className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
								isSubscribed
									? 'bg-background border border-primary/30 text-text hover:bg-primary/10'
									: 'bg-gradient-to-r from-primary to-accent text-white hover:opacity-90 btn-3d'
							}`}>
							{isSubscribed ? 'Disable' : 'Enable'}
						</button>
					</div>
				)}

				{/* Document Tokens Card */}
				{user && (
					<div className='bg-background border border-primary/20 rounded-lg p-4 flex items-center justify-between card'>
						<div className='flex items-center gap-4'>
							<div className='p-3 bg-primary/10 rounded-full'>
								<FaCoins className='w-5 h-5 text-accent' />
							</div>
							<div>
								<div className='flex items-center gap-2'>
									<h3 className='font-medium text-text'>Document Tokens</h3>
									<span className='px-2 py-0.5 text-sm bg-primary/10 text-primary rounded-full'>
										{user?.documentToken || 0}
									</span>
								</div>
								{user?.documentToken === 0 && user.isAdmin ? (
									<p className='text-sm text-red-600 dark:text-red-400 flex items-center gap-1 mt-1'>
										<FaExclamationCircle className='w-4 h-4' />
										Admin status at risk{' '}
										<Link
											href='/upgradetoadmin'
											className='flex items-center transition-all duration-300 text-primary underline cursor-pointer hover:text-accent'>
											Upgrade now
										</Link>
									</p>
								) : (
									<p className='text-sm text-text/70 mt-1'>
										Available for processing
									</p>
								)}
							</div>
						</div>
					</div>
				)}

				{/* Documents Received Card */}
				{!user.superAdmin && (
					<div className='bg-background border border-primary/20 rounded-lg p-4 flex items-center justify-between card-3d'>
						<div className='flex items-center gap-4'>
							<div className='p-3 bg-primary/10 rounded-full'>
								<FaFileAlt className='w-5 h-5 text-accent' />
							</div>
							<div>
								<h3 className='font-medium text-text'>Documents Received</h3>
								<p className='text-sm text-text/70'>Processed items</p>
							</div>
						</div>
						<span className='text-2xl font-semibold text-text'>
							{/* {getLoggedInAdminProjectCount() - 1 < 0
								? 0
								: getLoggedInAdminProjectCount() - 1} */}
							{user.documentsReceived}
						</span>
					</div>
				)}

				{/* SuperAdmin Stats - Admin List with Pagination */}
				{user.superAdmin && (
					<>
						<div className='col-span-1 md:col-span-2 lg:col-span-3'>
							<h3 className='text-xl font-semibold mb-4 text-primary'>
								Admin Users
							</h3>

							{loading.admins ? (
								<div className='flex justify-center p-8'>
									<FaUsers className='animate-spin h-8 w-8 text-primary' />
								</div>
							) : admins.length === 0 ? (
								<div className='text-center p-8 bg-primary/5 rounded-lg'>
									<FaUsers className='mx-auto h-12 w-12 text-primary/40 mb-4' />
									<p className='text-text/70'>No admin users found.</p>
								</div>
							) : (
								<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4'>
									{admins.map((admin) => (
										<div
											key={admin._id}
											className='bg-background border border-primary/20 rounded-lg p-4 card'>
											<div className='flex items-center gap-4 mb-3'>
												<div className='p-3 bg-primary/10 rounded-full'>
													<FaUsers className='w-5 h-5 text-accent' />
												</div>
												<div>
													<h3 className='font-medium text-text'>
														{admin.name}
													</h3>
													<p className='text-sm text-text/70'>Admin</p>
												</div>
											</div>
											<div className='grid grid-cols-2 gap-4'>
												<div className='flex items-center gap-2'>
													<FaFileAlt className='w-4 h-4 text-primary/70' />
													<span className='text-sm text-text/80'>
														{admin.documentsReceived || 0} docs
													</span>
												</div>
												<div className='flex items-center gap-2'>
													<FaCoins className='w-4 h-4 text-primary/70' />
													<span className='text-sm text-text/80'>
														{admin.documentToken || 0} tokens
													</span>
												</div>
											</div>
										</div>
									))}
								</div>
							)}

							{/* Pagination for admins */}
							<Pagination
								pagination={adminPagination}
								onPageChange={handlePageChange}
								onLimitChange={handleLimitChange}
								itemName='admins'
								currentItems={admins}
							/>
						</div>
					</>
				)}
			</div>
		</div>
	);
};

export default AdminStats;
