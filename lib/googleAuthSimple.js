/**
 * Simplified Google Sign-In utility for client-side authentication
 * This handles the Google Sign-In flow and returns the ID token
 */

/**
 * Load Google Sign-In script and initialize
 */
const loadGoogleSignIn = () => {
	return new Promise((resolve, reject) => {
		// Check if already loaded
		if (window.google && window.google.accounts) {
			resolve();
			return;
		}

		// Check if script is already in DOM
		if (document.getElementById('google-signin-script')) {
			// Wait for it to load
			const checkInterval = setInterval(() => {
				if (window.google && window.google.accounts) {
					clearInterval(checkInterval);
					resolve();
				}
			}, 100);
			
			// Timeout after 10 seconds
			setTimeout(() => {
				clearInterval(checkInterval);
				reject(new Error('Google Sign-In script load timeout'));
			}, 10000);
			return;
		}

		// Create and load script
		const script = document.createElement('script');
		script.id = 'google-signin-script';
		script.src = 'https://accounts.google.com/gsi/client';
		script.async = true;
		script.defer = true;
		
		script.onload = () => {
			// Wait a bit for the library to be ready
			setTimeout(() => {
				if (window.google && window.google.accounts) {
					resolve();
				} else {
					reject(new Error('Google Sign-In library not ready'));
				}
			}, 100);
		};
		
		script.onerror = () => {
			reject(new Error('Failed to load Google Sign-In script'));
		};
		
		document.head.appendChild(script);
	});
};

/**
 * Sign in with Google and get ID token
 */
export const signInWithGoogle = async () => {
	try {
		// Load the Google Sign-In library
		await loadGoogleSignIn();
		
		if (!process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID) {
			throw new Error('Google Client ID not configured. Please add NEXT_PUBLIC_GOOGLE_CLIENT_ID to your environment variables.');
		}

		return new Promise((resolve, reject) => {
			// Create a unique callback function
			const callbackId = 'google_callback_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
			
			// Set up the callback
			window[callbackId] = (response) => {
				// Clean up
				delete window[callbackId];
				
				if (response.credential) {
					resolve(response.credential);
				} else {
					reject(new Error('No credential received from Google'));
				}
			};

			try {
				// Initialize Google Sign-In
				window.google.accounts.id.initialize({
					client_id: process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID,
					callback: window[callbackId],
					auto_select: false,
					cancel_on_tap_outside: true,
				});

				// Try One Tap first
				window.google.accounts.id.prompt((notification) => {
					if (notification.isNotDisplayed() || notification.isSkippedMoment()) {
						// One Tap not available, try popup
						console.log('One Tap not available, trying popup...');
						
						// Clean up the callback since One Tap failed
						delete window[callbackId];
						
						// Try popup method
						tryPopupSignIn().then(resolve).catch(reject);
					}
				});

				// Set a timeout in case nothing happens
				setTimeout(() => {
					if (window[callbackId]) {
						delete window[callbackId];
						reject(new Error('Google Sign-In timeout. Please try again.'));
					}
				}, 30000); // 30 second timeout

			} catch (error) {
				delete window[callbackId];
				reject(new Error('Failed to initialize Google Sign-In: ' + error.message));
			}
		});

	} catch (error) {
		throw new Error('Google Sign-In failed: ' + error.message);
	}
};

/**
 * Fallback popup sign-in method
 */
const tryPopupSignIn = () => {
	return new Promise((resolve, reject) => {
		try {
			if (!window.google || !window.google.accounts || !window.google.accounts.oauth2) {
				reject(new Error('Google OAuth2 not available'));
				return;
			}

			const client = window.google.accounts.oauth2.initTokenClient({
				client_id: process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID,
				scope: 'openid email profile',
				callback: (response) => {
					if (response.access_token) {
						// We have an access token, but we need an ID token
						// For now, we'll reject and ask user to try again
						reject(new Error('Please try signing in again. If the problem persists, refresh the page.'));
					} else if (response.error) {
						reject(new Error('Google Sign-In failed: ' + response.error));
					} else {
						reject(new Error('No token received from Google'));
					}
				},
			});

			client.requestAccessToken();

		} catch (error) {
			reject(new Error('Popup sign-in failed: ' + error.message));
		}
	});
};

/**
 * Alternative method: Create a sign-in button
 */
export const createGoogleSignInButton = (containerId, onSuccess, onError) => {
	loadGoogleSignIn().then(() => {
		const container = document.getElementById(containerId);
		if (!container) {
			onError(new Error(`Container with ID '${containerId}' not found`));
			return;
		}

		// Clear container
		container.innerHTML = '';

		// Create callback
		const callbackId = 'google_button_callback_' + Date.now();
		window[callbackId] = (response) => {
			delete window[callbackId];
			if (response.credential) {
				onSuccess(response.credential);
			} else {
				onError(new Error('No credential received from Google'));
			}
		};

		// Initialize and render button
		window.google.accounts.id.initialize({
			client_id: process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID,
			callback: window[callbackId],
		});

		window.google.accounts.id.renderButton(container, {
			theme: 'outline',
			size: 'large',
			width: container.offsetWidth || 300,
		});

	}).catch(onError);
};
