'use client';
import { useState, useEffect, useContext } from 'react';
import { AuthContext } from '@/app/context/AuthContext';
import { NotificationContext } from '@/app/context/NotificationContext';
import toast from 'react-hot-toast';

export const useChatNotifications = () => {
	const { user, fetchWithToken } = useContext(AuthContext);
	const { notificationStatus, isSubscribed } = useContext(NotificationContext);
	const [chatNotificationsEnabled, setChatNotificationsEnabled] =
		useState(false);

	// Load chat notification preference from localStorage
	useEffect(() => {
		if (user) {
			const saved = localStorage.getItem(`chatNotifications_${user._id}`);
			setChatNotificationsEnabled(saved === 'true');
		}
	}, [user]);

	// Save chat notification preference to localStorage
	const toggleChatNotifications = (enabled) => {
		if (user) {
			localStorage.setItem(`chatNotifications_${user._id}`, enabled.toString());
			setChatNotificationsEnabled(enabled);

			if (enabled && notificationStatus !== 'granted') {
				toast.error('Please enable browser notifications first');
				return false;
			}

			toast.success(
				enabled ? 'Chat notifications enabled' : 'Chat notifications disabled',
			);
			return true;
		}
		return false;
	};

	// Send notification when new chat message is received
	const sendChatNotification = (message, projectTitle) => {
		if (!chatNotificationsEnabled || notificationStatus !== 'granted') return;

		if ('serviceWorker' in navigator) {
			navigator.serviceWorker.ready.then((registration) => {
				registration.showNotification('New Chat Message', {
					body: `${message.sender.name} in ${projectTitle}: ${message.content}`,
					icon: '/icon.png',
					badge: '/icon.png',
					tag: `chat-${message.chatId}`,
					requireInteraction: false,
					data: {
						url: `/dashboard?openChat=${message.chatId}`,
						chatId: message.chatId,
						messageId: message._id,
						timestamp: Date.now(),
					},
					actions: [
						{
							action: 'reply',
							title: 'Reply',
						},
						{
							action: 'view',
							title: 'View Chat',
						},
					],
				});
			});
		}
	};

	return {
		chatNotificationsEnabled,
		toggleChatNotifications,
		sendChatNotification,
		canReceiveNotifications: notificationStatus === 'granted' && isSubscribed,
	};
};
