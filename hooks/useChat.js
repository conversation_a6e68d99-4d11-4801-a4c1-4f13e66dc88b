'use client';
import { useState, useEffect, useCallback, useRef } from 'react';
import { useSocket } from '../app/context/SocketContext';

export const useChat = (chatId, projectId = null) => {
  const { isConnected, chatHandlers, joinChat, leaveChat, sendMessage, markMessagesRead } = useSocket();
  
  const [messages, setMessages] = useState([]);
  const [typingUsers, setTypingUsers] = useState(new Set());
  const [participants, setParticipants] = useState([]);
  const [isJoined, setIsJoined] = useState(false);
  const [loading, setLoading] = useState(false);
  
  const typingTimeoutRef = useRef(null);
  const messageCallbacksRef = useRef({});

  // Join chat when chatId changes
  useEffect(() => {
    if (chatId && isConnected) {
      const success = joinChat(chatId, projectId);
      if (success) {
        setIsJoined(true);
        setLoading(false);
      }
    }

    // Cleanup: leave chat when component unmounts or chatId changes
    return () => {
      if (chatId && isConnected) {
        leaveChat(chatId);
        setIsJoined(false);
      }
    };
  }, [chatId, projectId, isConnected, joinChat, leaveChat]);

  // Set up message handlers
  useEffect(() => {
    if (!chatHandlers) return;

    // New message handler
    const handleNewMessage = (messageData) => {
      // Only handle messages for this chat
      if (messageData.chatId === chatId) {
        setMessages(prev => {
          // Check if message already exists to avoid duplicates
          const messageExists = prev.some(msg => 
            (msg._id || msg.id) === (messageData._id || messageData.id)
          );
          
          if (!messageExists) {
            return [...prev, messageData].sort((a, b) => new Date(a.timestamp) - new Date(b.timestamp));
          }
          return prev;
        });
      }
    };

    // Message read handler
    const handleMessageRead = (data) => {
      if (data.chatId === chatId) {
        setMessages(prev => prev.map(msg => {
          if (data.messageIds.includes(msg._id || msg.id)) {
            return { ...msg, read: true, readBy: data.readBy };
          }
          return msg;
        }));
      }
    };

    // Typing indicators
    const handleTypingStart = (data) => {
      if (data.chatId === chatId) {
        setTypingUsers(prev => new Set([...prev, data.userId]));
      }
    };

    const handleTypingStop = (data) => {
      if (data.chatId === chatId) {
        setTypingUsers(prev => {
          const newSet = new Set(prev);
          newSet.delete(data.userId);
          return newSet;
        });
      }
    };

    // Participants status
    const handleParticipantsStatus = (data) => {
      if (data.chatId === chatId) {
        setParticipants(data.participants || []);
      }
    };

    // Register callbacks
    chatHandlers.onNewMessage(handleNewMessage);
    chatHandlers.onMessageRead(handleMessageRead);
    chatHandlers.onTypingStart(handleTypingStart);
    chatHandlers.onTypingStop(handleTypingStop);
    chatHandlers.onParticipantsStatus(handleParticipantsStatus);

    // Store references for cleanup
    messageCallbacksRef.current = {
      handleNewMessage,
      handleMessageRead,
      handleTypingStart,
      handleTypingStop,
      handleParticipantsStatus
    };

    // Get initial participants status
    if (isJoined) {
      chatHandlers.getChatParticipantsStatus(chatId);
    }

    // Cleanup
    return () => {
      const callbacks = messageCallbacksRef.current;
      if (callbacks) {
        chatHandlers.removeNewMessageCallback(callbacks.handleNewMessage);
        chatHandlers.removeMessageReadCallback(callbacks.handleMessageRead);
        chatHandlers.removeTypingStartCallback(callbacks.handleTypingStart);
        chatHandlers.removeTypingStopCallback(callbacks.handleTypingStop);
        chatHandlers.removeParticipantsStatusCallback(callbacks.handleParticipantsStatus);
      }
    };
  }, [chatId, chatHandlers, isJoined]);

  // Send message function
  const handleSendMessage = useCallback((message, messageType = 'text') => {
    if (!isConnected || !isJoined) {
      console.warn('Cannot send message: not connected or not joined to chat');
      return false;
    }

    const success = sendMessage(chatId, message, projectId, messageType);
    return success;
  }, [isConnected, isJoined, sendMessage, chatId, projectId]);

  // Send file message
  const handleSendFile = useCallback((fileData) => {
    if (!isConnected || !isJoined) {
      console.warn('Cannot send file: not connected or not joined to chat');
      return false;
    }

    chatHandlers.sendFileMessage(chatId, fileData, projectId);
    return true;
  }, [isConnected, isJoined, chatHandlers, chatId, projectId]);

  // Mark messages as read
  const handleMarkMessagesRead = useCallback((messageIds) => {
    if (!isConnected || !isJoined) {
      console.warn('Cannot mark messages as read: not connected or not joined to chat');
      return false;
    }

    const success = markMessagesRead(chatId, messageIds);
    return success;
  }, [isConnected, isJoined, markMessagesRead, chatId]);

  // Typing indicators
  const startTyping = useCallback(() => {
    if (!isConnected || !isJoined) return;

    chatHandlers.startTyping(chatId);
    
    // Clear existing timeout
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }
    
    // Stop typing after 3 seconds of inactivity
    typingTimeoutRef.current = setTimeout(() => {
      chatHandlers.stopTyping(chatId);
    }, 3000);
  }, [isConnected, isJoined, chatHandlers, chatId]);

  const stopTyping = useCallback(() => {
    if (!isConnected || !isJoined) return;

    chatHandlers.stopTyping(chatId);
    
    // Clear timeout
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
      typingTimeoutRef.current = null;
    }
  }, [isConnected, isJoined, chatHandlers, chatId]);

  // Load chat history (you would typically fetch this from your API)
  const loadChatHistory = useCallback(async (limit = 50, offset = 0) => {
    setLoading(true);
    try {
      // This would be your API call to fetch chat history
      // const response = await fetch(`/api/chats/${chatId}/messages?limit=${limit}&offset=${offset}`);
      // const data = await response.json();
      // setMessages(data.messages);
      
      // For now, we'll just set loading to false
      setLoading(false);
    } catch (error) {
      console.error('Error loading chat history:', error);
      setLoading(false);
    }
  }, [chatId]);

  // Get unread message count
  const getUnreadCount = useCallback(() => {
    return messages.filter(msg => !msg.read).length;
  }, [messages]);

  // Get last message
  const getLastMessage = useCallback(() => {
    return messages.length > 0 ? messages[messages.length - 1] : null;
  }, [messages]);

  return {
    // State
    messages,
    typingUsers: Array.from(typingUsers),
    participants,
    isJoined,
    isConnected,
    loading,
    
    // Actions
    sendMessage: handleSendMessage,
    sendFile: handleSendFile,
    markMessagesRead: handleMarkMessagesRead,
    startTyping,
    stopTyping,
    loadChatHistory,
    
    // Utilities
    getUnreadCount,
    getLastMessage,
    
    // Chat info
    chatId,
    projectId
  };
};

export default useChat;
