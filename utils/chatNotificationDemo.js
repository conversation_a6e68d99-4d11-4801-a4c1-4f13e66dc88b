// Demo script to test chat notifications
export const simulateNewChatMessage = (notificationService) => {
  const mockMessage = {
    _id: '123',
    content: 'Hello! This is a test message from the admin.',
    sender: {
      name: 'Admin User',
      isAdmin: true
    },
    chatId: 'abc123',
    createdAt: new Date()
  };
  
  const projectTitle = 'Sample Project Submission';
  
  // Send the notification
  notificationService.sendChatNotification(mockMessage, projectTitle);
  
  return mockMessage;
};

export const testNotificationPermission = async () => {
  if (!('Notification' in window)) {
    console.error('This browser does not support notifications');
    return false;
  }
  
  if (Notification.permission === 'granted') {
    console.log('Notifications are already enabled');
    return true;
  }
  
  if (Notification.permission === 'denied') {
    console.error('Notifications are blocked');
    return false;
  }
  
  try {
    const permission = await Notification.requestPermission();
    if (permission === 'granted') {
      console.log('Notification permission granted');
      return true;
    } else {
      console.error('Notification permission denied');
      return false;
    }
  } catch (error) {
    console.error('Error requesting notification permission:', error);
    return false;
  }
};

// Test function to send a simple browser notification
export const testBrowserNotification = () => {
  if (Notification.permission === 'granted') {
    new Notification('UploadDoc Chat Test', {
      body: 'This is a test notification from your chat system!',
      icon: '/icon.png',
      badge: '/icon.png',
      tag: 'test-notification',
    });
  } else {
    console.error('Notification permission not granted');
  }
};
