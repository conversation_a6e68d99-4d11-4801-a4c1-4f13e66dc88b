'use client';
import socketService from './socketService';

class NotificationHandlers {
  constructor() {
    this.notificationCallbacks = new Map();
    this.subscriptions = new Set();
  }

  // Initialize notification event listeners
  initialize() {
    this.setupNotificationListeners();
    this.requestNotificationPermission();
  }

  setupNotificationListeners() {
    // New notification received
    socketService.on('new_notification', (notificationData) => {
      console.log('New notification received:', notificationData);
      this.handleNewNotification(notificationData);
    });

    // Notification marked as read
    socketService.on('notification_marked_read', (data) => {
      console.log('Notification marked as read:', data);
      this.handleNotificationRead(data);
    });

    // Bulk notifications
    socketService.on('bulk_notifications', (notificationsData) => {
      console.log('Bulk notifications received:', notificationsData);
      this.handleBulkNotifications(notificationsData);
    });

    // Subscription confirmation
    socketService.on('subscribed_to_notifications', (data) => {
      console.log('Subscribed to notifications:', data);
      this.handleSubscriptionConfirmed(data);
    });

    // Unsubscription confirmation
    socketService.on('unsubscribed_from_notifications', (data) => {
      console.log('Unsubscribed from notifications:', data);
      this.handleUnsubscriptionConfirmed(data);
    });
  }

  // Request browser notification permission
  async requestNotificationPermission() {
    if ('Notification' in window) {
      const permission = await Notification.requestPermission();
      console.log('Notification permission:', permission);
      return permission === 'granted';
    }
    return false;
  }

  // Send a notification via socket
  sendNotification(notificationData) {
    if (!socketService.isSocketConnected()) {
      console.warn('Cannot send notification: socket not connected');
      return;
    }

    console.log('Sending notification:', notificationData);
    socketService.emit('send_notification', notificationData);
  }

  // Send bulk notifications
  sendBulkNotifications(notificationsArray) {
    if (!socketService.isSocketConnected()) {
      console.warn('Cannot send bulk notifications: socket not connected');
      return;
    }

    console.log('Sending bulk notifications:', notificationsArray);
    socketService.emit('send_bulk_notifications', { notifications: notificationsArray });
  }

  // Mark notification as read
  markNotificationRead(notificationId) {
    if (!socketService.isSocketConnected()) {
      console.warn('Cannot mark notification as read: socket not connected');
      return;
    }

    console.log('Marking notification as read:', notificationId);
    socketService.emit('mark_notification_read', { notificationId });
  }

  // Subscribe to notifications
  subscribeToNotifications(notificationTypes = []) {
    if (!socketService.isSocketConnected()) {
      console.warn('Cannot subscribe to notifications: socket not connected');
      return;
    }

    console.log('Subscribing to notifications:', notificationTypes);
    socketService.emit('subscribe_to_notifications', { types: notificationTypes });
  }

  // Unsubscribe from notifications
  unsubscribeFromNotifications(notificationTypes = []) {
    if (!socketService.isSocketConnected()) {
      console.warn('Cannot unsubscribe from notifications: socket not connected');
      return;
    }

    console.log('Unsubscribing from notifications:', notificationTypes);
    socketService.emit('unsubscribe_from_notifications', { types: notificationTypes });
  }

  // Event handlers
  handleNewNotification(notificationData) {
    // Show browser notification if permission granted
    this.showBrowserNotification(notificationData);

    // Trigger callbacks
    const callbacks = this.notificationCallbacks.get('new_notification') || [];
    callbacks.forEach(callback => {
      try {
        callback(notificationData);
      } catch (error) {
        console.error('Error in new notification callback:', error);
      }
    });
  }

  handleNotificationRead(data) {
    const callbacks = this.notificationCallbacks.get('notification_read') || [];
    callbacks.forEach(callback => {
      try {
        callback(data);
      } catch (error) {
        console.error('Error in notification read callback:', error);
      }
    });
  }

  handleBulkNotifications(notificationsData) {
    // Handle each notification
    if (notificationsData.notifications && Array.isArray(notificationsData.notifications)) {
      notificationsData.notifications.forEach(notification => {
        this.handleNewNotification(notification);
      });
    }

    // Trigger bulk callbacks
    const callbacks = this.notificationCallbacks.get('bulk_notifications') || [];
    callbacks.forEach(callback => {
      try {
        callback(notificationsData);
      } catch (error) {
        console.error('Error in bulk notifications callback:', error);
      }
    });
  }

  handleSubscriptionConfirmed(data) {
    this.subscriptions.add(data.type || 'general');
    
    const callbacks = this.notificationCallbacks.get('subscription_confirmed') || [];
    callbacks.forEach(callback => {
      try {
        callback(data);
      } catch (error) {
        console.error('Error in subscription confirmed callback:', error);
      }
    });
  }

  handleUnsubscriptionConfirmed(data) {
    this.subscriptions.delete(data.type || 'general');
    
    const callbacks = this.notificationCallbacks.get('unsubscription_confirmed') || [];
    callbacks.forEach(callback => {
      try {
        callback(data);
      } catch (error) {
        console.error('Error in unsubscription confirmed callback:', error);
      }
    });
  }

  // Show browser notification
  showBrowserNotification(notificationData) {
    if ('Notification' in window && Notification.permission === 'granted') {
      const options = {
        body: notificationData.message || notificationData.content,
        icon: '/icon.png', // Your app icon
        badge: '/icon.png',
        tag: notificationData.id || notificationData._id,
        requireInteraction: notificationData.priority === 'high',
        data: notificationData
      };

      const notification = new Notification(
        notificationData.title || 'UploadDoc Notification',
        options
      );

      // Handle notification click
      notification.onclick = (event) => {
        event.preventDefault();
        window.focus();
        
        // Handle navigation based on notification type
        this.handleNotificationClick(notificationData);
        
        notification.close();
      };

      // Auto close after 5 seconds for non-high priority notifications
      if (notificationData.priority !== 'high') {
        setTimeout(() => notification.close(), 5000);
      }
    }
  }

  // Handle notification click navigation
  handleNotificationClick(notificationData) {
    try {
      switch (notificationData.type) {
        case 'chat_message':
          if (notificationData.chatId) {
            window.location.href = `/chat?chatId=${notificationData.chatId}`;
          } else {
            window.location.href = '/chat';
          }
          break;
        
        case 'project_status':
        case 'project_progress':
        case 'project_reassigned':
          if (notificationData.projectId) {
            window.location.href = `/dashboard?projectId=${notificationData.projectId}`;
          } else {
            window.location.href = '/dashboard';
          }
          break;
        
        case 'deadline_update':
          window.location.href = '/dashboard';
          break;
        
        default:
          window.location.href = '/dashboard';
      }
    } catch (error) {
      console.error('Error handling notification click:', error);
      window.location.href = '/dashboard';
    }
  }

  // Callback registration methods
  onNewNotification(callback) {
    this.registerCallback('new_notification', callback);
  }

  onNotificationRead(callback) {
    this.registerCallback('notification_read', callback);
  }

  onBulkNotifications(callback) {
    this.registerCallback('bulk_notifications', callback);
  }

  onSubscriptionConfirmed(callback) {
    this.registerCallback('subscription_confirmed', callback);
  }

  onUnsubscriptionConfirmed(callback) {
    this.registerCallback('unsubscription_confirmed', callback);
  }

  // Helper method to register callbacks
  registerCallback(event, callback) {
    if (!this.notificationCallbacks.has(event)) {
      this.notificationCallbacks.set(event, []);
    }
    this.notificationCallbacks.get(event).push(callback);
  }

  // Helper method to unregister callbacks
  removeCallback(event, callback) {
    const callbacks = this.notificationCallbacks.get(event);
    if (callbacks) {
      const index = callbacks.indexOf(callback);
      if (index > -1) {
        callbacks.splice(index, 1);
      }
    }
  }

  // Remove specific callbacks
  removeNewNotificationCallback(callback) {
    this.removeCallback('new_notification', callback);
  }

  removeNotificationReadCallback(callback) {
    this.removeCallback('notification_read', callback);
  }

  removeBulkNotificationsCallback(callback) {
    this.removeCallback('bulk_notifications', callback);
  }

  removeSubscriptionConfirmedCallback(callback) {
    this.removeCallback('subscription_confirmed', callback);
  }

  removeUnsubscriptionConfirmedCallback(callback) {
    this.removeCallback('unsubscription_confirmed', callback);
  }

  // Utility methods
  getActiveSubscriptions() {
    return Array.from(this.subscriptions);
  }

  isSubscribedTo(notificationType) {
    return this.subscriptions.has(notificationType);
  }

  // Cleanup method
  cleanup() {
    this.notificationCallbacks.clear();
    this.subscriptions.clear();
  }
}

// Create singleton instance
const notificationHandlers = new NotificationHandlers();

export default notificationHandlers;
