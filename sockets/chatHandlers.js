'use client';
import socketService from './socketService';

class ChatHandlers {
  constructor() {
    this.activeChats = new Set();
    this.messageCallbacks = new Map();
    this.typingCallbacks = new Map();
    this.onlineUsersCallbacks = new Map();
  }

  // Initialize chat event listeners
  initialize() {
    this.setupChatListeners();
  }

  setupChatListeners() {
    // New message received
    socketService.on('new_message', (messageData) => {
      console.log('New message received:', messageData);
      this.handleNewMessage(messageData);
    });

    // Message marked as read
    socketService.on('message_marked_read', (data) => {
      console.log('Message marked as read:', data);
      this.handleMessageRead(data);
    });

    // User started typing
    socketService.on('typing_start', (data) => {
      console.log('User started typing:', data);
      this.handleTypingStart(data);
    });

    // User stopped typing
    socketService.on('typing_stop', (data) => {
      console.log('User stopped typing:', data);
      this.handleTypingStop(data);
    });

    // File message uploaded
    socketService.on('file_message_uploaded', (data) => {
      console.log('File message uploaded:', data);
      this.handleNewMessage(data);
    });

    // Chat participants status
    socketService.on('chat_participants_status', (data) => {
      console.log('Chat participants status:', data);
      this.handleParticipantsStatus(data);
    });

    // User online/offline status
    socketService.on('user_online', (userData) => {
      this.handleUserOnline(userData);
    });

    socketService.on('user_offline', (userData) => {
      this.handleUserOffline(userData);
    });
  }

  // Join a chat room
  joinChat(chatId, projectId = null) {
    if (!socketService.isSocketConnected()) {
      console.warn('Cannot join chat: socket not connected');
      return;
    }

    console.log('Joining chat:', { chatId, projectId });
    socketService.emit('join_chat', { chatId, projectId });
    this.activeChats.add(chatId);
  }

  // Leave a chat room
  leaveChat(chatId) {
    if (!socketService.isSocketConnected()) {
      console.warn('Cannot leave chat: socket not connected');
      return;
    }

    console.log('Leaving chat:', chatId);
    socketService.emit('leave_chat', { chatId });
    this.activeChats.delete(chatId);
  }

  // Send a message
  sendMessage(chatId, message, projectId = null, messageType = 'text') {
    if (!socketService.isSocketConnected()) {
      console.warn('Cannot send message: socket not connected');
      return;
    }

    const messageData = {
      chatId,
      message,
      projectId,
      messageType,
      timestamp: new Date().toISOString()
    };

    console.log('Sending message:', messageData);
    socketService.emit('send_message', messageData);
  }

  // Send file message
  sendFileMessage(chatId, fileData, projectId = null) {
    if (!socketService.isSocketConnected()) {
      console.warn('Cannot send file message: socket not connected');
      return;
    }

    const messageData = {
      chatId,
      file: fileData,
      projectId,
      messageType: 'file',
      timestamp: new Date().toISOString()
    };

    console.log('Sending file message:', messageData);
    socketService.emit('file_message_upload', messageData);
  }

  // Mark messages as read
  markMessagesRead(chatId, messageIds) {
    if (!socketService.isSocketConnected()) {
      console.warn('Cannot mark messages as read: socket not connected');
      return;
    }

    console.log('Marking messages as read:', { chatId, messageIds });
    socketService.emit('mark_messages_read', { chatId, messageIds });
  }

  // Start typing indicator
  startTyping(chatId) {
    if (!socketService.isSocketConnected()) {
      console.warn('Cannot start typing: socket not connected');
      return;
    }

    socketService.emit('typing_start', { chatId });
  }

  // Stop typing indicator
  stopTyping(chatId) {
    if (!socketService.isSocketConnected()) {
      console.warn('Cannot stop typing: socket not connected');
      return;
    }

    socketService.emit('typing_stop', { chatId });
  }

  // Get chat participants status
  getChatParticipantsStatus(chatId) {
    if (!socketService.isSocketConnected()) {
      console.warn('Cannot get participants status: socket not connected');
      return;
    }

    console.log('Getting chat participants status:', chatId);
    socketService.emit('get_chat_participants_status', { chatId });
  }

  // Event handlers
  handleNewMessage(messageData) {
    const callbacks = this.messageCallbacks.get('new_message') || [];
    callbacks.forEach(callback => {
      try {
        callback(messageData);
      } catch (error) {
        console.error('Error in new message callback:', error);
      }
    });
  }

  handleMessageRead(data) {
    const callbacks = this.messageCallbacks.get('message_read') || [];
    callbacks.forEach(callback => {
      try {
        callback(data);
      } catch (error) {
        console.error('Error in message read callback:', error);
      }
    });
  }

  handleTypingStart(data) {
    const callbacks = this.typingCallbacks.get('typing_start') || [];
    callbacks.forEach(callback => {
      try {
        callback(data);
      } catch (error) {
        console.error('Error in typing start callback:', error);
      }
    });
  }

  handleTypingStop(data) {
    const callbacks = this.typingCallbacks.get('typing_stop') || [];
    callbacks.forEach(callback => {
      try {
        callback(data);
      } catch (error) {
        console.error('Error in typing stop callback:', error);
      }
    });
  }

  handleParticipantsStatus(data) {
    const callbacks = this.onlineUsersCallbacks.get('participants_status') || [];
    callbacks.forEach(callback => {
      try {
        callback(data);
      } catch (error) {
        console.error('Error in participants status callback:', error);
      }
    });
  }

  handleUserOnline(userData) {
    const callbacks = this.onlineUsersCallbacks.get('user_online') || [];
    callbacks.forEach(callback => {
      try {
        callback(userData);
      } catch (error) {
        console.error('Error in user online callback:', error);
      }
    });
  }

  handleUserOffline(userData) {
    const callbacks = this.onlineUsersCallbacks.get('user_offline') || [];
    callbacks.forEach(callback => {
      try {
        callback(userData);
      } catch (error) {
        console.error('Error in user offline callback:', error);
      }
    });
  }

  // Callback registration methods
  onNewMessage(callback) {
    this.registerCallback('new_message', callback, this.messageCallbacks);
  }

  onMessageRead(callback) {
    this.registerCallback('message_read', callback, this.messageCallbacks);
  }

  onTypingStart(callback) {
    this.registerCallback('typing_start', callback, this.typingCallbacks);
  }

  onTypingStop(callback) {
    this.registerCallback('typing_stop', callback, this.typingCallbacks);
  }

  onParticipantsStatus(callback) {
    this.registerCallback('participants_status', callback, this.onlineUsersCallbacks);
  }

  onUserOnline(callback) {
    this.registerCallback('user_online', callback, this.onlineUsersCallbacks);
  }

  onUserOffline(callback) {
    this.registerCallback('user_offline', callback, this.onlineUsersCallbacks);
  }

  // Helper method to register callbacks
  registerCallback(event, callback, callbackMap) {
    if (!callbackMap.has(event)) {
      callbackMap.set(event, []);
    }
    callbackMap.get(event).push(callback);
  }

  // Helper method to unregister callbacks
  removeCallback(event, callback, callbackMap) {
    const callbacks = callbackMap.get(event);
    if (callbacks) {
      const index = callbacks.indexOf(callback);
      if (index > -1) {
        callbacks.splice(index, 1);
      }
    }
  }

  // Remove specific callbacks
  removeNewMessageCallback(callback) {
    this.removeCallback('new_message', callback, this.messageCallbacks);
  }

  removeMessageReadCallback(callback) {
    this.removeCallback('message_read', callback, this.messageCallbacks);
  }

  removeTypingStartCallback(callback) {
    this.removeCallback('typing_start', callback, this.typingCallbacks);
  }

  removeTypingStopCallback(callback) {
    this.removeCallback('typing_stop', callback, this.typingCallbacks);
  }

  removeParticipantsStatusCallback(callback) {
    this.removeCallback('participants_status', callback, this.onlineUsersCallbacks);
  }

  removeUserOnlineCallback(callback) {
    this.removeCallback('user_online', callback, this.onlineUsersCallbacks);
  }

  removeUserOfflineCallback(callback) {
    this.removeCallback('user_offline', callback, this.onlineUsersCallbacks);
  }

  // Cleanup method
  cleanup() {
    this.activeChats.clear();
    this.messageCallbacks.clear();
    this.typingCallbacks.clear();
    this.onlineUsersCallbacks.clear();
  }

  // Get active chats
  getActiveChats() {
    return Array.from(this.activeChats);
  }
}

// Create singleton instance
const chatHandlers = new ChatHandlers();

export default chatHandlers;
