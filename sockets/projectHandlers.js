'use client';
import socketService from './socketService';

class ProjectHandlers {
  constructor() {
    this.projectCallbacks = new Map();
    this.subscribedProjects = new Set();
  }

  // Initialize project event listeners
  initialize() {
    this.setupProjectListeners();
  }

  setupProjectListeners() {
    // Project status updated
    socketService.on('project_status_updated', (projectData) => {
      console.log('Project status updated:', projectData);
      this.handleProjectStatusUpdate(projectData);
    });

    // Project progress updated
    socketService.on('project_progress_updated', (projectData) => {
      console.log('Project progress updated:', projectData);
      this.handleProjectProgressUpdate(projectData);
    });

    // Project reassigned
    socketService.on('project_reassigned', (projectData) => {
      console.log('Project reassigned:', projectData);
      this.handleProjectReassigned(projectData);
    });

    // Project deadline updated
    socketService.on('project_deadline_updated', (projectData) => {
      console.log('Project deadline updated:', projectData);
      this.handleProjectDeadlineUpdate(projectData);
    });

    // Subscription to project updates confirmed
    socketService.on('subscribed_to_project_updates', (data) => {
      console.log('Subscribed to project updates:', data);
      this.handleProjectSubscriptionConfirmed(data);
    });

    // Unsubscription from project updates confirmed
    socketService.on('unsubscribed_from_project_updates', (data) => {
      console.log('Unsubscribed from project updates:', data);
      this.handleProjectUnsubscriptionConfirmed(data);
    });
  }

  // Update project status
  updateProjectStatus(projectId, status, userId = null) {
    if (!socketService.isSocketConnected()) {
      console.warn('Cannot update project status: socket not connected');
      return;
    }

    const updateData = {
      projectId,
      status,
      userId,
      timestamp: new Date().toISOString()
    };

    console.log('Updating project status:', updateData);
    socketService.emit('update_project_status', updateData);
  }

  // Reassign project
  reassignProject(projectId, newAssigneeId, reassignedBy) {
    if (!socketService.isSocketConnected()) {
      console.warn('Cannot reassign project: socket not connected');
      return;
    }

    const reassignData = {
      projectId,
      newAssigneeId,
      reassignedBy,
      timestamp: new Date().toISOString()
    };

    console.log('Reassigning project:', reassignData);
    socketService.emit('reassign_project', reassignData);
  }

  // Update project deadline
  updateProjectDeadline(projectId, newDeadline, updatedBy) {
    if (!socketService.isSocketConnected()) {
      console.warn('Cannot update project deadline: socket not connected');
      return;
    }

    const deadlineData = {
      projectId,
      newDeadline,
      updatedBy,
      timestamp: new Date().toISOString()
    };

    console.log('Updating project deadline:', deadlineData);
    socketService.emit('update_project_deadline', deadlineData);
  }

  // Update project progress
  updateProjectProgress(projectId, progress, updatedBy) {
    if (!socketService.isSocketConnected()) {
      console.warn('Cannot update project progress: socket not connected');
      return;
    }

    const progressData = {
      projectId,
      progress,
      updatedBy,
      timestamp: new Date().toISOString()
    };

    console.log('Updating project progress:', progressData);
    socketService.emit('update_project_progress', progressData);
  }

  // Subscribe to project updates
  subscribeToProjectUpdates(projectId) {
    if (!socketService.isSocketConnected()) {
      console.warn('Cannot subscribe to project updates: socket not connected');
      return;
    }

    console.log('Subscribing to project updates:', projectId);
    socketService.emit('subscribe_to_project_updates', { projectId });
  }

  // Unsubscribe from project updates
  unsubscribeFromProjectUpdates(projectId) {
    if (!socketService.isSocketConnected()) {
      console.warn('Cannot unsubscribe from project updates: socket not connected');
      return;
    }

    console.log('Unsubscribing from project updates:', projectId);
    socketService.emit('unsubscribe_from_project_updates', { projectId });
  }

  // Event handlers
  handleProjectStatusUpdate(projectData) {
    const callbacks = this.projectCallbacks.get('project_status_updated') || [];
    callbacks.forEach(callback => {
      try {
        callback(projectData);
      } catch (error) {
        console.error('Error in project status update callback:', error);
      }
    });
  }

  handleProjectProgressUpdate(projectData) {
    const callbacks = this.projectCallbacks.get('project_progress_updated') || [];
    callbacks.forEach(callback => {
      try {
        callback(projectData);
      } catch (error) {
        console.error('Error in project progress update callback:', error);
      }
    });
  }

  handleProjectReassigned(projectData) {
    const callbacks = this.projectCallbacks.get('project_reassigned') || [];
    callbacks.forEach(callback => {
      try {
        callback(projectData);
      } catch (error) {
        console.error('Error in project reassigned callback:', error);
      }
    });
  }

  handleProjectDeadlineUpdate(projectData) {
    const callbacks = this.projectCallbacks.get('project_deadline_updated') || [];
    callbacks.forEach(callback => {
      try {
        callback(projectData);
      } catch (error) {
        console.error('Error in project deadline update callback:', error);
      }
    });
  }

  handleProjectSubscriptionConfirmed(data) {
    if (data.projectId) {
      this.subscribedProjects.add(data.projectId);
    }
    
    const callbacks = this.projectCallbacks.get('project_subscription_confirmed') || [];
    callbacks.forEach(callback => {
      try {
        callback(data);
      } catch (error) {
        console.error('Error in project subscription confirmed callback:', error);
      }
    });
  }

  handleProjectUnsubscriptionConfirmed(data) {
    if (data.projectId) {
      this.subscribedProjects.delete(data.projectId);
    }
    
    const callbacks = this.projectCallbacks.get('project_unsubscription_confirmed') || [];
    callbacks.forEach(callback => {
      try {
        callback(data);
      } catch (error) {
        console.error('Error in project unsubscription confirmed callback:', error);
      }
    });
  }

  // Callback registration methods
  onProjectStatusUpdate(callback) {
    this.registerCallback('project_status_updated', callback);
  }

  onProjectProgressUpdate(callback) {
    this.registerCallback('project_progress_updated', callback);
  }

  onProjectReassigned(callback) {
    this.registerCallback('project_reassigned', callback);
  }

  onProjectDeadlineUpdate(callback) {
    this.registerCallback('project_deadline_updated', callback);
  }

  onProjectSubscriptionConfirmed(callback) {
    this.registerCallback('project_subscription_confirmed', callback);
  }

  onProjectUnsubscriptionConfirmed(callback) {
    this.registerCallback('project_unsubscription_confirmed', callback);
  }

  // Helper method to register callbacks
  registerCallback(event, callback) {
    if (!this.projectCallbacks.has(event)) {
      this.projectCallbacks.set(event, []);
    }
    this.projectCallbacks.get(event).push(callback);
  }

  // Helper method to unregister callbacks
  removeCallback(event, callback) {
    const callbacks = this.projectCallbacks.get(event);
    if (callbacks) {
      const index = callbacks.indexOf(callback);
      if (index > -1) {
        callbacks.splice(index, 1);
      }
    }
  }

  // Remove specific callbacks
  removeProjectStatusUpdateCallback(callback) {
    this.removeCallback('project_status_updated', callback);
  }

  removeProjectProgressUpdateCallback(callback) {
    this.removeCallback('project_progress_updated', callback);
  }

  removeProjectReassignedCallback(callback) {
    this.removeCallback('project_reassigned', callback);
  }

  removeProjectDeadlineUpdateCallback(callback) {
    this.removeCallback('project_deadline_updated', callback);
  }

  removeProjectSubscriptionConfirmedCallback(callback) {
    this.removeCallback('project_subscription_confirmed', callback);
  }

  removeProjectUnsubscriptionConfirmedCallback(callback) {
    this.removeCallback('project_unsubscription_confirmed', callback);
  }

  // Utility methods
  getSubscribedProjects() {
    return Array.from(this.subscribedProjects);
  }

  isSubscribedToProject(projectId) {
    return this.subscribedProjects.has(projectId);
  }

  // Auto-subscribe to projects for a user
  autoSubscribeToUserProjects(userProjects) {
    if (!Array.isArray(userProjects)) {
      console.warn('userProjects should be an array');
      return;
    }

    userProjects.forEach(project => {
      const projectId = project._id || project.id;
      if (projectId && !this.isSubscribedToProject(projectId)) {
        this.subscribeToProjectUpdates(projectId);
      }
    });
  }

  // Batch subscribe to multiple projects
  batchSubscribeToProjects(projectIds) {
    if (!Array.isArray(projectIds)) {
      console.warn('projectIds should be an array');
      return;
    }

    projectIds.forEach(projectId => {
      if (!this.isSubscribedToProject(projectId)) {
        this.subscribeToProjectUpdates(projectId);
      }
    });
  }

  // Batch unsubscribe from multiple projects
  batchUnsubscribeFromProjects(projectIds) {
    if (!Array.isArray(projectIds)) {
      console.warn('projectIds should be an array');
      return;
    }

    projectIds.forEach(projectId => {
      if (this.isSubscribedToProject(projectId)) {
        this.unsubscribeFromProjectUpdates(projectId);
      }
    });
  }

  // Cleanup method
  cleanup() {
    this.projectCallbacks.clear();
    this.subscribedProjects.clear();
  }
}

// Create singleton instance
const projectHandlers = new ProjectHandlers();

export default projectHandlers;
