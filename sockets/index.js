// Socket.IO client modules for UploadDoc frontend

// Core socket service
export { default as socketService } from './socketService';

// Handler modules
export { default as chatHandlers } from './chatHandlers';
export { default as notificationHandlers } from './notificationHandlers';
export { default as projectHandlers } from './projectHandlers';

// Context and hooks
export { SocketProvider, useSocket } from '../app/context/SocketContext';
export { useChat } from '../hooks/useChat';

// Components
export { default as SocketStatus } from '../components/SocketStatus';

// Socket events and utilities
export const SOCKET_EVENTS = {
  // Connection events
  CONNECT: 'connect',
  DISCONNECT: 'disconnect',
  CONNECTION_ESTABLISHED: 'connection_established',
  CONNECTION_FAILED: 'connection_failed',
  AUTHENTICATED: 'authenticated',
  AUTHENTICATION_ERROR: 'authentication_error',
  
  // Chat events
  JOIN_CHAT: 'join_chat',
  LEAVE_CHAT: 'leave_chat',
  SEND_MESSAGE: 'send_message',
  NEW_MESSAGE: 'new_message',
  MARK_MESSAGES_READ: 'mark_messages_read',
  MESSAGE_MARKED_READ: 'message_marked_read',
  TYPING_START: 'typing_start',
  TYPING_STOP: 'typing_stop',
  FILE_MESSAGE_UPLOAD: 'file_message_upload',
  FILE_MESSAGE_UPLOADED: 'file_message_uploaded',
  GET_CHAT_PARTICIPANTS_STATUS: 'get_chat_participants_status',
  CHAT_PARTICIPANTS_STATUS: 'chat_participants_status',
  
  // Notification events
  SEND_NOTIFICATION: 'send_notification',
  NEW_NOTIFICATION: 'new_notification',
  SEND_BULK_NOTIFICATIONS: 'send_bulk_notifications',
  BULK_NOTIFICATIONS: 'bulk_notifications',
  MARK_NOTIFICATION_READ: 'mark_notification_read',
  NOTIFICATION_MARKED_READ: 'notification_marked_read',
  SUBSCRIBE_TO_NOTIFICATIONS: 'subscribe_to_notifications',
  UNSUBSCRIBE_FROM_NOTIFICATIONS: 'unsubscribe_from_notifications',
  SUBSCRIBED_TO_NOTIFICATIONS: 'subscribed_to_notifications',
  UNSUBSCRIBED_FROM_NOTIFICATIONS: 'unsubscribed_from_notifications',
  
  // Project events
  UPDATE_PROJECT_STATUS: 'update_project_status',
  PROJECT_STATUS_UPDATED: 'project_status_updated',
  REASSIGN_PROJECT: 'reassign_project',
  PROJECT_REASSIGNED: 'project_reassigned',
  UPDATE_PROJECT_DEADLINE: 'update_project_deadline',
  PROJECT_DEADLINE_UPDATED: 'project_deadline_updated',
  UPDATE_PROJECT_PROGRESS: 'update_project_progress',
  PROJECT_PROGRESS_UPDATED: 'project_progress_updated',
  SUBSCRIBE_TO_PROJECT_UPDATES: 'subscribe_to_project_updates',
  UNSUBSCRIBE_FROM_PROJECT_UPDATES: 'unsubscribe_from_project_updates',
  SUBSCRIBED_TO_PROJECT_UPDATES: 'subscribed_to_project_updates',
  UNSUBSCRIBED_FROM_PROJECT_UPDATES: 'unsubscribed_from_project_updates',
  
  // User status events
  USER_ONLINE: 'user_online',
  USER_OFFLINE: 'user_offline'
};

// Message types
export const MESSAGE_TYPES = {
  TEXT: 'text',
  FILE: 'file',
  IMAGE: 'image',
  SYSTEM: 'system'
};

// Notification types
export const NOTIFICATION_TYPES = {
  CHAT_MESSAGE: 'chat_message',
  PROJECT_STATUS: 'project_status',
  PROJECT_PROGRESS: 'project_progress',
  PROJECT_REASSIGNED: 'project_reassigned',
  DEADLINE_UPDATE: 'deadline_update',
  SYSTEM: 'system'
};

// Project status types
export const PROJECT_STATUSES = {
  PENDING: 'pending',
  IN_PROGRESS: 'in_progress',
  COMPLETED: 'completed',
  CANCELLED: 'cancelled',
  ON_HOLD: 'on_hold'
};

// Connection status types
export const CONNECTION_STATUSES = {
  CONNECTED: 'connected',
  CONNECTING: 'connecting',
  DISCONNECTED: 'disconnected',
  ERROR: 'error',
  AUTH_ERROR: 'auth_error'
};

// Utility functions
export const socketUtils = {
  /**
   * Check if socket is connected
   * @returns {boolean}
   */
  isConnected: () => {
    return socketService.isSocketConnected();
  },
  
  /**
   * Get current socket ID
   * @returns {string|null}
   */
  getSocketId: () => {
    const socket = socketService.getSocket();
    return socket ? socket.id : null;
  },
  
  /**
   * Wait for socket connection
   * @param {number} timeout - Timeout in milliseconds
   * @returns {Promise<Socket>}
   */
  waitForConnection: (timeout = 5000) => {
    return socketService.waitForConnection(timeout);
  }
};
