# Google Sign-In Setup Instructions

## Overview
The frontend has been updated to work with your new backend Google authentication endpoint (`/api/auth/google/login`) that expects an `idToken` from the client-side Google Sign-In.

## Changes Made

### 1. Updated Dependencies
- Added `google-auth-library` package for handling Google authentication

### 2. New Files Created
- `lib/googleAuthSimple.js` - Simplified Google Sign-In utility
- `GOOGLE_SIGNIN_SETUP.md` - This setup guide

### 3. Updated Files
- `app/context/AuthContext.js` - Updated `googleLogin` function to use client-side authentication
- `app/auth/login/page.js` - Added loading states and error handling for Google Sign-In
- `.env` - Added required environment variables (needs configuration)

## Required Environment Variables

You need to add the following to your `.env` file:

```env
NEXT_PUBLIC_GOOGLE_CLIENT_ID=your_actual_google_client_id_here
NEXT_PUBLIC_BACKEND_URL=your_actual_backend_url_here
```

## How to Get Google Client ID

1. Go to the [Google Cloud Console](https://console.cloud.google.com/)
2. Select your project or create a new one
3. Enable the Google+ API and Google Identity API
4. Go to "Credentials" in the left sidebar
5. Click "Create Credentials" > "OAuth 2.0 Client IDs"
6. Choose "Web application"
7. Add your domain to "Authorized JavaScript origins":
   - For development: `http://localhost:3000`
   - For production: `https://yourdomain.com`
8. Copy the Client ID and add it to your `.env` file

## Backend URL Configuration

Set `NEXT_PUBLIC_BACKEND_URL` to your backend server URL:
- For development: `http://localhost:8000` (or whatever port your backend uses)
- For production: `https://your-backend-domain.com`

## How It Works

1. User clicks the Google Sign-In button
2. Frontend loads Google Sign-In library
3. Google One Tap or popup appears
4. User authenticates with Google
5. Google returns an ID token to the frontend
6. Frontend sends the ID token to your backend endpoint: `POST /api/auth/google/login`
7. Backend verifies the token and returns user data + JWT token
8. Frontend stores the JWT token and user data

## Testing

After configuring the environment variables:

1. Start your backend server
2. Start the frontend: `npm run dev`
3. Go to the login page
4. Click the Google Sign-In button
5. Complete the Google authentication flow

## Troubleshooting

### Common Issues

1. **"Google Client ID not configured"**
   - Make sure `NEXT_PUBLIC_GOOGLE_CLIENT_ID` is set in `.env`
   - Restart your development server after adding environment variables

2. **"Google Sign-In script load timeout"**
   - Check your internet connection
   - Make sure Google's servers are accessible

3. **"Google login failed"**
   - Check that your backend is running and accessible
   - Verify the backend endpoint is correct (`/api/auth/google/login`)
   - Check browser console for detailed error messages

4. **CORS Issues**
   - Make sure your backend allows requests from your frontend domain
   - Add appropriate CORS headers to your backend

### Debug Mode

To enable debug logging, open browser console and look for:
- Google Sign-In initialization messages
- Network requests to your backend
- Any error messages

## Security Notes

- The Google Client ID is safe to expose in frontend code
- Never expose your Google Client Secret in frontend code
- The ID token is only valid for a short time and should be verified by your backend
- Always validate the ID token on your backend before creating user sessions

## Next Steps

1. Configure the environment variables
2. Test the Google Sign-In flow
3. Verify that user data is correctly stored in your backend
4. Test the complete authentication flow including logout and token refresh
